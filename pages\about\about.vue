<template>
	<view class="about-container">

		<view class="about-content">
			<view class="section-title">
				<text>买腕表 · 查报价 · 验真伪 — 就上值达</text>
			</view>
			
			<view class="section-content">
				<text class="app-description">1. “三大首创”技术壁垒:基于智能AI大模型与数字区块链，打开腕表行业三个第一：腕表AI报价，腕表AI鉴定，腕表电话AI报价；</text>
				<text class="app-description">2. AI鉴定技术的微观检测能力（机芯/材质级）；</text>
				<text class="app-description">3. 数字链在腕表资产确权提供关键作用；</text>
				<text class="app-description">4. 为腕表商家和腕表爱好者群体提供核心收益参考；</text>
				<text class="app-description">5. 用数据量化平台赋能（10万+数据库/9万+表款覆盖）</text>
			</view>
		</view>
		

		
		<view class="contact-section">
			<view class="section-title">
				<text>联系我们</text>
			</view>
			
			<view class="contact-list">
				<view class="contact-item">
					<view class="contact-label">
						<text>客服电话：</text>
					</view>
					<view class="contact-value">
						<text>0512-68090363</text>
					</view>
				</view>
				
				<view class="contact-item">
					<view class="contact-label">
						<text>客服邮箱：</text>
					</view>
					<view class="contact-value">
						<text><EMAIL></text>
					</view>
				</view>
				
				<view class="contact-item">
					<view class="contact-label">
						<text>公司地址：</text>
					</view>
					<view class="contact-value">
						<text>江苏省苏州市高新区狮山路76号</text>
					</view>
				</view>
				
				<view class="contact-item">
					<view class="contact-label">
						<text>工作时间：</text>
					</view>
					<view class="contact-value">
						<text>周一至周五 9:00-18:00</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="copyright">
			<text>© 2025 版权所有</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	methods: {
		// 跳转到相应页面
		goToPage(type) {
			switch(type) {
				case 'company':
				case 'team':
					uni.navigateTo({
						url: `/pages/about/${type}`
					});
					break;
				case 'user':
				case 'privacy':
					uni.navigateTo({
						url: `/pages/agreement/agreement?type=${type}`
					});
					break;
				default:
					break;
			}
		},
		// 打开社交媒体
		openSocial(platform) {
			// 实际开发中，这里应该跳转到对应的社交媒体页面或者打开小程序、公众号二维码等
			uni.showToast({
				title: `即将打开${this.getSocialName(platform)}`,
				icon: 'none'
			});
		},
		// 获取社交媒体名称
		getSocialName(platform) {
			switch(platform) {
				case 'weixin': return '微信公众号';
				case 'weibo': return '微博官方账号';
				case 'douyin': return '抖音官方账号';
				case 'xiaohongshu': return '小红书官方账号';
				default: return '';
			}
		}
	}
}
</script>

<style>
.about-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 30px;
}

.about-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40px 0;
	background-color: #fff;
}

.app-logo {
	width: 100px;
	height: 100px;
	border-radius: 20px;
	margin-bottom: 15px;
}

.app-name {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.app-version {
	font-size: 14px;
	color: #999;
}

.about-content {
	margin: 15px 15px 0;
	padding: 15px;
	background-color: #fff;
	border-radius: 8px;
}

.section-title {
	margin-bottom: 10px;
}

.section-title text {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.section-content {
	display: flex;
	flex-direction: column;
	margin-bottom: 10px;
}

.app-description {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	text-align: justify;
}

.about-list {
	margin: 15px 15px 0;
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
}

.about-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.about-item:last-child {
	border-bottom: none;
}

.item-name {
	font-size: 16px;
	color: #333;
}

.contact-section {
	margin: 15px 15px 0;
	padding: 15px;
	background-color: #fff;
	border-radius: 8px;
}

.contact-list {
	margin-top: 10px;
}

.contact-item {
	display: flex;
	margin-bottom: 10px;
}

.contact-item:last-child {
	margin-bottom: 0;
}

.contact-label {
	width: 90px;
}

.contact-label text {
	font-size: 14px;
	color: #666;
}

.contact-value text {
	font-size: 14px;
	color: #333;
}

.social-section {
	margin: 15px 15px 0;
	padding: 15px;
	background-color: #fff;
	border-radius: 8px;
}

.social-title {
	margin-bottom: 15px;
}

.social-title text {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.social-icons {
	display: flex;
	justify-content: space-around;
}

.social-icon {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.icon-image {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	margin-bottom: 5px;
}

.icon-name {
	font-size: 12px;
	color: #666;
}

.copyright {
	margin-top: 30px;
	text-align: center;
}

.copyright text {
	font-size: 12px;
	color: #999;
}
</style> 