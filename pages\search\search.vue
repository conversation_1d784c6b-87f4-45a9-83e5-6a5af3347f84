<template>
    <view class="search-container" :class="{'has-nav-height': true}" :style="{'--nav-height': navHeight + 'px'}">
        <!-- #ifdef APP-PLUS -->
        <!-- 搜索导航栏 -->
        <view class="search-nav">
            <view class="search-header">
                <view class="back-btn" @click="goBack">
                    <uni-icons type="back" size="24" color="#1a1a1a"></uni-icons>
                </view>
                <view class="search-input-wrapper">
                    <input 
                        class="search-input" 
                        type="text" 
                        v-model="searchKeyword" 
                        placeholder="搜索品牌、型号..." 
                        confirm-type="search"
                        @confirm="handleSearch"
                        @input="handleRealTimeSearch"
                        focus
                    />
                    <text class="clear-icon" v-if="searchKeyword" @click="clearKeyword">✕</text>
                </view>
                <view class="search-btn" @click="handleSearch">搜索</view>
            </view>
        </view>
        <!-- #endif -->

        <!-- #ifdef MP-WEIXIN -->
        <view class="search-nav" :style="navBarStyle">
            <view class="search-header" :style="searchBarStyle">
                <view class="back-btn" @click="goBack">
                    <uni-icons type="back" size="24" color="#1a1a1a"></uni-icons>
                </view>
                <view class="search-input-wrapper">
                    <input 
                        class="search-input" 
                        type="text" 
                        v-model="searchKeyword" 
                        placeholder="搜索品牌、型号..." 
                        confirm-type="search"
                        @confirm="handleSearch"
                        @input="handleRealTimeSearch"
                        focus
                    />
                    <text class="clear-icon" v-if="searchKeyword" @click="clearKeyword">✕</text>
                </view>
                <view class="search-btn" @click="handleSearch">搜索</view>
            </view>
        </view>
        <!-- #endif -->

        <!-- 搜索历史 -->
        <view class="search-history" v-if="!searchKeyword && searchHistory.length > 0 && !showResults" :style="contentStyle">
            <view class="history-header">
                <text class="history-title">搜索历史</text>
                <text class="clear-history" @click="clearHistory">清除</text>
            </view>
            <view class="history-list">
                <view 
                    class="history-item" 
                    v-for="(item, index) in searchHistory" 
                    :key="index"
                    @click="useHistoryKeyword(item)"
                >
                    <text class="history-text">{{ item }}</text>
                </view>
            </view>
        </view>

        <!-- 热门搜索 -->
        <view class="hot-search" v-if="!searchKeyword && !showResults" :style="contentStyle">
            <view class="hot-header">
                <text class="hot-title">热门搜索</text>
            </view>
            <view class="hot-tags">
                <view 
                    class="hot-tag" 
                    v-for="(item, index) in hotSearches" 
                    :key="index"
                    @click="useHistoryKeyword(item)"
                >
                    {{ item }}
                </view>
            </view>
        </view>

        <!-- 实时搜索结果 -->
        <view class="realtime-results" v-if="searchKeyword && realtimeResults.length > 0 && !showResults" :style="contentStyle">
            <view class="realtime-list">
                <view 
                    class="realtime-item" 
                    v-for="(item, index) in realtimeResults" 
                    :key="index"
                    @click="goToDetail(item.id)"
                >
                    <image class="realtime-image" :src="item.imageMain" mode="aspectFit"></image>
                    <view class="realtime-info">
                        <view class="realtime-name" v-html="highlightText(item.brand + ' ' + item.series)"></view>
                        <view class="realtime-model" v-html="highlightText(item.referenceNumber || '')"></view>
                        <view class="realtime-specs">{{ item.movementType }} {{ item.caseMaterial }} {{ item.diameter }}</view>
                        <view class="realtime-price">¥{{ item.officePrice || '--' }}</view>
                    </view>
                </view>
            </view>
            <view class="view-more-btn" @click="handleSearch">
                查看更多
            </view>
        </view>

        <!-- 搜索结果 -->
        <view class="search-results" v-if="showResults" :style="contentStyle">
            <view class="filter-tabs">
                <view 
                    class="filter-tab" 
                    :class="{ active: orderField === 'id' }"
                    @click="setFilter('id')"
                >综合</view>
                <view 
                    class="filter-tab" 
                    :class="{ active: orderField === 'official_quote' }"
                    @click="setFilter('official_quote')"
                >
                    价格
                    <text class="sort-icon">{{ orderBy === 'asc' ? '↑' : '↓' }}</text>
                </view>
            </view>

            <!-- 结果列表 - 改为两列显示 -->
            <view class="result-grid" v-if="searchResults.length > 0">
                <view 
                    class="result-grid-item" 
                    v-for="(item, index) in searchResults" 
                    :key="index"
                    @click="goToDetail(item.id)"
                >
                    <view class="result-grid-image-container">
                        <image class="result-grid-image" :src="item.imageMain" mode="aspectFit"></image>
                        <!-- <view class="watch-favorite">
                            <uni-icons type="star" size="16" color="#999"></uni-icons>
                            <text class="favorite-count">{{ item.favoriteCount || Math.floor(Math.random() * 100) }}</text>
                        </view> -->
                    </view>
                    <view class="result-grid-info">
                        <view class="brand-series-line">
                            <text class="result-grid-brand">{{ item.brand + ' ' + item.series }}</text>
                        </view>
                        <text class="result-grid-model">{{ item.referenceNumber || '' }}</text>
                        <text class="result-grid-specs">{{ item.movementType }} {{ item.caseMaterial }} {{ item.diameter }} {{ item.dialColor }}</text>
                        <view class="result-grid-price-container">
                            <view class="price-row">
                                <text class="result-grid-retail-price">¥{{ item.officePrice || '--' }}</text>
                                <text class="price-label">公价</text>
                            </view>
                            <view class="price-row" v-if="item.usedMarketPrice && item.usedMarketPrice != '--'">
                                <text class="result-grid-market-price" :class="{'price-down': item.usedMarketPrice}">¥{{ item.usedMarketPrice || '' }}</text>
                                <text class="price-label">行情价</text>
                                <!-- <view class="price-trend">
                                    <uni-icons type="arrow-down" size="16" color="#1a1a1a"></uni-icons>
                                </view> -->
                            </view>
                        </view>
                    </view>
                </view>
            </view>
                
            <!-- 加载更多提示 -->
            <view class="loading-more" v-if="searchResults.length > 0">
                <text v-if="loading">加载中...</text>
                <text v-else-if="!hasMore">没有更多数据了</text>
                <text v-else @click="loadMore">点击加载更多</text>
            </view>

            <!-- 无结果提示 -->
            <view class="no-result" v-else>
                <text class="no-result-text">未找到相关商品</text>
                <text class="no-result-tip">换个关键词试试吧</text>
            </view>

        </view>
    </view>
</template>

<script>
import { searchProducts } from '@/utils/api.js'

export default {
    data() {
        return {
            searchKeyword: '', // 搜索关键词
            searchHistory: [], // 搜索历史
            hotSearches: ['劳力士', '百达翡丽', '欧米茄', '限量款', '热门腕表'], // 热门搜索
            showResults: false, // 是否显示搜索结果
            searchResults: [], // 搜索结果

            pageNum: 1,
            pageSize: 20,
            orderField: null,
            orderBy: null,

            loading: false, // 是否加载中
            hasMore: true, // 是否有更多数据

            realtimeResults: [], // 实时搜索结果
            realtimeSearchTimer: null, // 实时搜索防抖计时器

            requestId: '', // 请求ID

            // 导航栏相关数据
            menuButtonInfo: {
                width: 0,
                height: 0,
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            },
            navBarHeight: 0,
            statusBarHeight: 0,
            searchBarStyle: {},
            navBarStyle: {},
            contentStyle: {},
            navHeight: 0, // 新增：存储实际导航栏高度
        }
    },
    onLoad() {
        // 获取本地存储的搜索历史
        try {
            const history = uni.getStorageSync('searchHistory');
            if (history) {
                this.searchHistory = JSON.parse(history);
            }
        } catch (e) {
            console.error('获取搜索历史失败', e);
        }

        // #ifdef MP-WEIXIN
        this.getStatusBarHeight();
        // #endif
    },
    methods: {
        // 获取状态栏高度和胶囊按钮信息
        getStatusBarHeight() {
            try {
                // 获取系统信息
                const systemInfo = uni.getSystemInfoSync();
                // 获取胶囊按钮的位置信息
                const menuButton = uni.getMenuButtonBoundingClientRect();
                
                // 保存状态栏高度
                this.statusBarHeight = systemInfo.statusBarHeight;
                
                // 保存胶囊按钮信息
                this.menuButtonInfo = {
                    width: menuButton.width,
                    height: menuButton.height,
                    top: menuButton.top,
                    right: menuButton.right,
                    bottom: menuButton.bottom,
                    left: menuButton.left
                };
                
                // 计算导航栏高度（使用胶囊按钮的bottom值，这样更准确）
                const navBarHeight = menuButton.height + 8; // 胶囊高度 + 上下边距
                this.navBarHeight = navBarHeight;
                
                // 设置实际的导航栏总高度（状态栏 + 导航栏）
                this.navHeight = this.statusBarHeight + navBarHeight;
                
                // 计算搜索栏的宽度（屏幕宽度 - 右侧胶囊按钮占用的空间 - 左右边距）
                const searchWidth = systemInfo.windowWidth - (systemInfo.windowWidth - menuButton.left) - 30;
                
                // 设置搜索栏样式
                this.searchBarStyle = {
                    width: `${searchWidth}px`,
                    height: `${menuButton.height}px`,
                    marginTop: '6px', // 调整上边距
                    marginRight: `${systemInfo.windowWidth - menuButton.left}px`,
                    display: 'flex',
                    alignItems: 'center',
                    padding: '0 15px'
                };
                
                // 设置导航栏样式
                this.navBarStyle = {
                    height: `${this.statusBarHeight + navBarHeight}px`,
                    paddingTop: `${this.statusBarHeight}px`,
                    backgroundColor: '#fff',
                    position: 'fixed',
                    top: '0',
                    left: '0',
                    width: '100%',
                    zIndex: '99',
                    borderBottom: '1px solid #f0f0f0'
                };
                
                // 更新内容区域的padding-top
                this.contentStyle = {
                    paddingTop: `${this.statusBarHeight + navBarHeight}px`
                };
                
            } catch (error) {
                console.error('获取导航栏信息失败：', error);
            }
        },
        // 随机生成请求ID
        generateRequestId() {

            if (!this.requestId) {
                this.requestId = Math.random().toString(36).substring(2, 15);
            }

            return this.requestId;
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        
        // 清空搜索关键词
        clearKeyword() {
            this.searchKeyword = '';
            this.showResults = false;
        },
        
        // 使用历史搜索词
        useHistoryKeyword(keyword) {
            this.searchKeyword = keyword;
            this.handleSearch();
        },
        
        // 清除搜索历史
        clearHistory() {
            uni.showModal({
                title: '提示',
                content: '确定要清除所有搜索历史吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.searchHistory = [];
                        uni.setStorageSync('searchHistory', JSON.stringify([]));
                    }
                }
            });
        },
        
        // 搜索处理
        handleSearch() {
            if (!this.searchKeyword.trim()) {
                uni.showToast({
                    title: '请输入搜索关键词',
                    icon: 'none'
                });
                return;
            }
            
            // 将当前关键词添加到搜索历史
            this.addToHistory(this.searchKeyword);
            
            // 清空实时搜索结果
            this.realtimeResults = [];
            
            // 显示加载中
            uni.showLoading({
                title: '搜索中'
            });
            
            this.loading = true;
            this.pageNum = 1; // 重置页码
            this.hasMore = true; // 重置加载更多状态
            
            // 调用搜索API
            this.fetchSearchResults();
        },
        
        // 获取搜索结果
        fetchSearchResults() {
            // 构建搜索参数
            const searchParams = {
                searchKeyword: this.searchKeyword,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                orderField: this.orderField,
                orderBy: this.orderBy,
                requestId: this.generateRequestId()
            };
            
            console.log(searchParams);
            

            searchProducts(searchParams)
                .then(res => {
                    console.log('搜索结果:', res);
                    
                    if (this.pageNum === 1) {
                        // 首次搜索，直接替换结果
                        this.searchResults = res.rows || [];
                    } else {
                        // 加载更多时，追加结果
                        this.searchResults = [...this.searchResults, ...(res.rows || [])];
                    }
                    
                    this.showResults = true;
                    this.hasMore = res.rows && res.rows.length >= this.pageSize;
                    
                    // 添加标签等处理
                    this.processSearchResults();
                })
                .catch(err => {
                    console.error('搜索失败:', err);
                    uni.showToast({
                        title: err.message || '网络异常，请重试',
                        icon: 'none'
                    });
                })
                .finally(() => {
                    this.loading = false;
                    uni.hideLoading();
                });
        },
        
        // 处理搜索结果数据
        processSearchResults() {
            this.searchResults = this.searchResults.map(item => {
                // 处理标签
                if (!item.tags) {
                    item.tags = this.extractTags(item);
                }
                return item;
            });
        },
        
        // 从商品数据中提取标签
        extractTags(item) {
            const tags = [];
            
            // 这里根据实际数据结构提取标签
            // 示例：如果商品是新品，添加new标签
            if (item.isNew) {
                tags.push('new');
            }
            
            // 如果商品销量高，添加hot标签
            if (item.sales && item.sales > 50) {
                tags.push('hot');
            }
            
            // 如果商品是限量版，添加limited标签
            if (item.isLimited) {
                tags.push('limited');
            }
            
            // 如果商品正在特惠，添加sale标签
            if (item.isOnSale) {
                tags.push('sale');
            }
            
            return tags;
        },
        
        // 加载更多数据
        loadMore() {
            if (this.loading || !this.hasMore) return;
            
            this.pageNum += 1;
            this.loading = true;
            
            this.fetchSearchResults();
        },
        
        // 添加到搜索历史
        addToHistory(keyword) {
            // 如果已存在，先移除再添加到最前面
            const index = this.searchHistory.indexOf(keyword);
            if (index > -1) {
                this.searchHistory.splice(index, 1);
            }
            
            // 添加到搜索历史首位
            this.searchHistory.unshift(keyword);
            
            // 限制历史记录数量为10条
            if (this.searchHistory.length > 10) {
                this.searchHistory = this.searchHistory.slice(0, 10);
            }
            
            // 保存到本地存储
            uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
        },
        
        // 设置筛选条件
        setFilter(filter) {
            if (this.orderField === filter) {
                // 如果点击的是当前已选中的过滤条件，切换排序方向
                this.orderBy = this.orderBy === 'asc' ? 'desc' : 'asc';
            } else {
                // 切换过滤字段
                this.orderField = filter;
                this.orderBy = 'asc';
            }
            
            // 如果已经有搜索结果，直接根据新的筛选条件重新搜索
            if (this.searchResults.length > 0) {
                this.pageNum = 1; // 重置页码
                this.fetchSearchResults();
            }
        },
        
        // 跳转到商品详情
        goToDetail(id) {
            uni.navigateTo({
                url: `/pages/product/detail?id=${id}`
            });
        },
        
        // 根据tag类型获取显示名称
        getTagName(tag) {
            const tagNames = {
                'hot': '热销',
                'limited': '限量',
                'new': '新品',
                'sale': '特惠'
            };
            return tagNames[tag] || tag;
        },

        // 实时搜索处理
        handleRealTimeSearch() {
            // 如果搜索关键词为空，清空实时搜索结果
            if (!this.searchKeyword.trim()) {
                this.realtimeResults = [];
                return;
            }
            
            // 设置实时搜索限制数量
            const realtimeLimit = 5;
            
            // 构建实时搜索参数
            const searchParams = {
                searchKeyword: this.searchKeyword,
                pageNum: 1,
                pageSize: realtimeLimit, // 限制实时搜索结果数量
                orderField: this.orderField,
                orderBy: this.orderBy,
                requestId: this.generateRequestId()
            };
			
			console.log(searchParams);
            
            // 添加防抖，避免频繁请求
            clearTimeout(this.realtimeSearchTimer);
            this.realtimeSearchTimer = setTimeout(() => {
                // 调用API进行实时搜索
                searchProducts(searchParams)
                    .then(res => {
                        this.realtimeResults = res.rows || [];
                    })
                    .catch(err => {
                        console.error('实时搜索失败:', err);
                        this.realtimeResults = [];
                    });
            }, 300); // 300ms防抖延迟
        },
        
        // 高亮显示匹配的文本
        highlightText(text) {
            if (!text || !this.searchKeyword) return text;
            
            const keyword = this.searchKeyword.trim();
            if (!keyword) return text;
            
            try {
                // 1. 分割搜索关键词（按特殊字符和空格分割）
                const keywordParts = keyword.split(/[^a-zA-Z0-9\u4e00-\u9fa5]+/).filter(part => part.length > 0);
                
                // 2. 如果没有有效的关键词部分，返回原始文本
                if (keywordParts.length === 0) return text;
                
                // 3. 对文本中的每个关键词部分进行高亮处理
                let result = text;
                
                // 对每个关键词部分进行处理
                for (const part of keywordParts) {
                    if (part.length === 0) continue;
                    
                    // 对待高亮的部分进行转义
                    const escapedPart = part.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    
                    // 创建一个匹配这个部分的正则表达式
                    const regex = new RegExp(`(${escapedPart})`, 'gi');
                    
                    // 替换匹配到的部分
                    result = result.replace(regex, '<text class="highlight-text">$1</text>');
                }
                
                return result;
            } catch (e) {
                console.error('高亮处理错误:', e);
                return text; // 发生错误时返回原始文本
            }
        }
    },
    // 下拉触底加载更多
    onReachBottom() {
        if (this.showResults) {
            this.loadMore();
        }
    }
}
</script>

<style>
/* APP端样式 */
/* #ifdef APP-PLUS */
.search-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 99;
    background-color: #fff;
    padding-top: var(--status-bar-height);
    box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.search-header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 15px;
}

.search-history, 
.hot-search, 
.search-results,
.realtime-results {
    padding: 15px;
}

.realtime-results {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
/* #endif */

/* 小程序端样式 */
/* #ifdef MP-WEIXIN */
.search-nav {
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    box-sizing: border-box;
}

.search-header {
    display: flex;
    align-items: center;
}

.search-history, 
.hot-search, 
.search-results,
.realtime-results {
    padding: 15px;
    margin-top: 15px;
}

.realtime-results {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    background-color: #fff;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    height: 32px !important;
    background-color: #f5f5f5;
    border-radius: 18px;
    padding: 0 10px;
    margin-right: 6px;
}

/* #endif */

/* 通用样式 */
.search-container {
    background-color: #f8f8f8;
    min-height: 100vh;
}

.back-btn {
    padding: 8px 8px 8px 0;
    display: flex;
    align-items: center;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    background-color: #f5f5f5;
    border-radius: 18px;
    padding: 0 15px;
    margin-right: 6px;
}

.search-input {
    flex: 1;
    height: 100%;
    font-size: 14px;
    background-color: transparent;
}

.clear-icon {
    font-size: 14px;
    color: #999;
    padding: 5px;
}

.search-btn {
    font-size: 14px;
    color: #1a1a1a;
    padding: 8px 0 8px 8px;
    white-space: nowrap;
}

/* 搜索历史 */
.history-header, .hot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.history-title, .hot-title {
    font-size: 16px;
    color: #333;
    font-weight: bold;
}

.clear-history {
    font-size: 14px;
    color: #999;
}

.history-list {
    display: flex;
    flex-wrap: wrap;
}

.history-item {
    background-color: #f0f0f0;
    border-radius: 15px;
    padding: 6px 12px;
    margin: 0 10px 10px 0;
}

.history-text {
    font-size: 13px;
    color: #666;
}

/* 热门搜索 */
.hot-tags {
    display: flex;
    flex-wrap: wrap;
}

.hot-tag {
    background-color: #f0f0f0;
    border-radius: 15px;
    padding: 6px 12px;
    margin: 0 10px 10px 0;
    font-size: 13px;
    color: #666;
}

/* 筛选标签 */
.filter-tabs {
    display: flex;
    background-color: #fff;
    padding: 10px 0;
    margin-bottom: 10px;
    border-radius: 8px;
}

.filter-tab {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #666;
    padding: 5px 0;
    position: relative;
}

.filter-tab.active {
    color: #1a1a1a;
    font-weight: bold;
}

.sort-icon {
    margin-left: 3px;
}

/* 搜索结果 - 网格布局 */
.result-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 10px;
}

.result-grid-item {
    width: 48%;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.result-grid-image-container {
    position: relative;
    width: 100%;
    height: 150px;
    background-color: #fff;
}

.result-grid-image {
    width: 100%;
    height: 150px;
    object-fit: contain;
}

.watch-favorite {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    background-color: rgba(255,255,255,0.8);
    border-radius: 12px;
    padding: 2px 6px;
}

.favorite-count {
    font-size: 12px;
    color: #666;
    margin-left: 2px;
}

.result-grid-info {
    padding: 10px;
}

.brand-series-line {
    margin-bottom: 5px;
}

.result-grid-brand {
    font-size: 14px;
    color: #333;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-grid-model {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-grid-specs {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-grid-price-container {
    margin-top: 5px;
}

.price-row {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.result-grid-retail-price {
    font-size: 16px;
    color: #000000;
    margin-right: 5px;
    font-weight: bold;
}

.result-grid-market-price {
    font-size: 16px;
    color: #000000;
    margin-right: 5px;
    font-weight: bold;
}

.price-label {
    font-size: 11px;
    color: #999;
    padding: 1px 4px;
    background-color: #f5f5f5;
    border-radius: 2px;
}

.price-down {
    color: #000000;
}

.price-trend {
    margin-left: 5px;
}

.result-grid-tags {
    display: flex;
    flex-wrap: wrap;
    margin: 5px 0;
}

.result-tag {
    font-size: 10px;
    color: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    margin-right: 5px;
    margin-bottom: 3px;
}

.result-grid-price {
    font-size: 15px;
    color: #3a559f;
    font-weight: bold;
    display: block;
    margin-top: 5px;
}

.hot {
    background-color: #ff6b6b;
}

.limited {
    background-color: #6b66ff;
}

.new {
    background-color: #66c887;
}

.sale {
    background-color: #ff9f43;
}

/* 保留原有样式用于其他组件 */
.result-list {
    margin-top: 10px;
}

.result-item {
    display: flex;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 12px;
}

.result-image {
    width: 100px;
    height: 100px;
    border-radius: 5px;
    background-color: #f5f5f5;
}

.result-info {
    flex: 1;
    padding-left: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.result-header {
    margin-bottom: 5px;
}

.result-name {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    display: block;
}

.result-brand {
    font-size: 13px;
    color: #666;
    margin-top: 3px;
    display: block;
}

.result-tags {
    display: flex;
    margin: 5px 0;
}

.result-bottom {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.result-price {
    font-size: 16px;
    color: #3a559f;
    font-weight: bold;
}

.result-sales {
    font-size: 12px;
    color: #999;
}

/* 无结果提示 */
.no-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.no-result-image {
    width: 120px;
    height: 120px;
    margin-bottom: 15px;
}

.no-result-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.no-result-tip {
    font-size: 14px;
    color: #999;
}

/* 加载更多提示 */
.loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
    color: #666;
    font-size: 14px;
}

.loading-more text {
    padding: 8px 20px;
    border-radius: 20px;
    background-color: #f5f5f5;
}

/* 实时搜索结果 */
.realtime-results {
    margin-top: calc(56px + var(--status-bar-height));
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 10px 15px;
}

.realtime-list {
    margin-bottom: 15px;
}

.realtime-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.realtime-image {
    width: 100px;
    height: 100px;
    border-radius: 5px;
    margin-right: 10px;
}

.realtime-info {
    flex: 1;
}

.realtime-name {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.realtime-model {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.realtime-specs {
    font-size: 12px;
    color: #999;
}

.realtime-price {
    font-size: 16px;
    color: #1a1a1a;
    font-weight: bold;
}

.view-more-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background-color: #1a1a1a;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    margin-top: 10px;
}

/* 高亮样式 */
.highlight-text {
    color: #1a1a1a;
    font-weight: bold;
    background-color: rgba(58, 85, 159, 0.1);
    padding: 0 2px;
}
</style> 