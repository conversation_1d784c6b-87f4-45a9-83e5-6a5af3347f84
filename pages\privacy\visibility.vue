<template>
	<view class="visibility-container">
		<view class="options-list">
			<view 
				class="option-item" 
				v-for="(item, index) in visibilityOptions" 
				:key="index"
				@click="selectOption(item.value)"
				:class="{ active: currentValue === item.value }"
			>
				<view class="option-content">
					<text class="option-name">{{ item.name }}</text>
					<text class="option-desc">{{ item.description }}</text>
				</view>
				<uni-icons v-if="currentValue === item.value" type="checkmarkempty" size="20" color="#3a559f"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			type: '', // 'profile' 或 'activity'
			currentValue: '', // 当前选中的值
			visibilityOptions: [
				{
					name: '所有人可见',
					value: 'all',
					description: '所有用户都可以查看'
				},
				{
					name: '仅好友可见',
					value: 'friends',
					description: '只有您的好友才能查看'
				},
				{
					name: '仅自己可见',
					value: 'none',
					description: '只有您自己可以查看'
				}
			]
		}
	},
	onLoad(options) {
		if (options.type) {
			this.type = options.type;
		}
		
		if (options.value) {
			this.currentValue = options.value;
		}
	},
	methods: {
		// 选择可见性选项
		selectOption(value) {
			if (this.currentValue === value) return;
			
			this.currentValue = value;
			
			// 实际开发中，这里应该调用API保存设置
			setTimeout(() => {
				uni.showToast({
					title: '设置已保存',
					icon: 'none'
				});
				
				// 返回上一页并传递选择的值
				setTimeout(() => {
					const pages = getCurrentPages();
					const prevPage = pages[pages.length - 2];
					
					// 向上一页传递选择的值
					if (prevPage && prevPage.$vm) {
						if (this.type === 'profile') {
							prevPage.$vm.settings.profileVisibility = this.currentValue;
						} else if (this.type === 'activity') {
							prevPage.$vm.settings.activityVisibility = this.currentValue;
						}
					}
					
					uni.navigateBack();
				}, 1000);
			}, 500);
		}
	}
}
</script>

<style>
.visibility-container {
	min-height: 100vh;
	background-color: #f8f9fc;
}

.options-list {
	margin-top: 10px;
	background-color: #fff;
}

.option-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.option-item:last-child {
	border-bottom: none;
}

.option-item.active {
	background-color: rgba(58, 85, 159, 0.05);
}

.option-content {
	display: flex;
	flex-direction: column;
}

.option-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
}

.option-desc {
	font-size: 14px;
	color: #999;
}
</style> 