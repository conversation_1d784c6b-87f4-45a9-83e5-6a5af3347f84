<!-- pages/index/index.vue -->
<template>
	<view class="container">
		<!-- 图标占位 -->
		<view class="icon-placeholder"></view>

		<!-- 标题 -->
		<view class="title">腕表报价</view>

		<!-- 描述 -->
		<view class="description">
			专业报价，一键获取市场最优价格
		</view>

		<!-- 占位空间 -->
		<view class="spacer"></view>

		<!-- 按钮 -->
		<view class="button-group">
			<view class="button primary" @click="goToLogin">登录</view>
			<view class="button secondary" @click="goToRegister">注册新账号</view>
		</view>
	</view>
</template>

<script setup>
	// 跳转到登录页面
	const goToLogin = () => {
		uni.navigateTo({
			url: '/pages/login/login'
		});
	};

	// 跳转到注册页面
	const goToRegister = () => {
		uni.navigateTo({
			url: '/pages/register/register'
		});
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100vh;
		background: linear-gradient(180deg, #3a5199 0%, #3a5199 100%);
		padding: 0 30rpx;
		box-sizing: border-box;
	}
	
	.hide {
		opacity: 0;
	}

	.icon-placeholder {
		width: 120rpx;
		height: 120rpx;
		background: #fff;
		border-radius: 20rpx;
		margin-top: 100rpx;
		/* 调整图标距离顶部的距离 */
		margin-bottom: 40rpx;
		opacity: 0;
	}

	.title {
		font-size: 48rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 20rpx;
	}

	.description {
		font-size: 28rpx;
		color: #fff;
		text-align: center;
		line-height: 40rpx;
		margin-bottom: 60rpx;
	}

	.highlight {
		color: #fff;
		font-weight: bold;
	}

	.spacer {
		flex: 1;
		/* 占满剩余空间，将按钮推到底部 */
	}

	.button-group {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 30rpx;
		margin-bottom: 40rpx;
		/* 按钮距离底部的间距 */
	}

	.button {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 10rpx;
		box-sizing: border-box;
	}

	.primary {
		background: #fff;
		color: #2A4B9B;
		font-weight: bold;
	}

	.secondary {
		background: transparent;
		border: 2rpx solid #fff;
		color: #fff;
	}
</style>