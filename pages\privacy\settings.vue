<template>
	<view class="privacy-container">
		<view class="privacy-section">
			<!-- <view class="section-title">
				<text>隐私保护</text>
			</view> -->
			
			<view class="privacy-list">
				<!-- <view class="privacy-item">
					<view class="item-info">
						<text class="item-name">浏览记录</text>
						<text class="item-desc">控制是否记录您的浏览历史</text>
					</view>
					<switch :checked="settings.enableBrowsingHistory" color="#3a559f" @change="toggleSetting('enableBrowsingHistory')" />
				</view> -->
				
				<view class="privacy-item">
					<view class="item-info">
						<text class="item-name">位置信息</text>
						<text class="item-desc">控制是否使用您的位置信息</text>
					</view>
					<switch :checked="settings.enableLocationInfo" color="#1a1a1a" @change="toggleSetting('enableLocationInfo')" />
				</view>
				
				<view class="privacy-item">
					<view class="item-info">
						<text class="item-name">广告个性化</text>
						<text class="item-desc">根据您的兴趣推荐广告内容</text>
					</view>
					<switch :checked="settings.enableAdPersonalization" color="#1a1a1a" @change="toggleSetting('enableAdPersonalization')" />
				</view>
				
				<view class="privacy-item" @click="clearData">
					<view class="item-info">
						<text class="item-name">清除个人数据</text>
						<text class="item-desc">清除账号中存储的个人数据</text>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				// 隐私保护
				enableBrowsingHistory: true,
				enableLocationInfo: true,
				enableAdPersonalization: false
			}
		}
	},
	onLoad() {
		// 实际开发中，这里应该从API获取用户隐私设置
		// 现在使用临时数据
	},
	methods: {
		// 切换设置项
		toggleSetting(key) {
			this.settings[key] = !this.settings[key];
			
			// 实际开发中，这里应该调用API保存设置
			this.saveSettings();
		},
		// 保存设置
		saveSettings() {
			// uni.showToast({
			// 	title: '设置已保存',
			// 	icon: 'none'
			// });
		},
		// 清除个人数据
		clearData() {
			uni.showModal({
				title: '清除个人数据',
				content: '这将清除您在应用中存储的所有个人数据，包括浏览记录、搜索历史等。此操作不可撤销，是否继续？',
				success: (res) => {
					if (res.confirm) {
						// 实际开发中，这里应该调用API清除用户数据
						
						uni.showLoading({
							title: '正在清除...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							
							uni.showToast({
								title: '数据已清除',
								icon: 'success'
							});
						}, 1500);
					}
				}
			});
		}
	}
}
</script>

<style>
.privacy-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 30px;
}

.privacy-section {
	margin-bottom: 10px;
}

.section-title {
	padding: 15px 15px 10px;
}

.section-title text {
	font-size: 15px;
	color: #666;
}

.privacy-list {
	background-color: #fff;
}

.privacy-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.privacy-item:last-child {
	border-bottom: none;
}

.item-info {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.item-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
}

.item-desc {
	font-size: 14px;
	color: #999;
}

.item-action {
	display: flex;
	align-items: center;
}

.action-value {
	font-size: 14px;
	color: #666;
	margin-right: 5px;
}
</style> 