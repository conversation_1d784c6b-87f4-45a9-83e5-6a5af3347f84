<template>
	<view class="container">
		<!-- Logo and Welcome -->
		<view class="header-container">
			<view class="logo-container">
				<image class="logo" src="https://www.zhida.net/app-resource/icon/w-logo.png" mode="aspectFit"></image>
			</view>
			<view class="welcome-container">
				<text class="welcome-text">Hi~ 欢迎来到值达!</text>
			</view>
		</view>

		<!-- Register Form -->
		<view class="form-container">
			<view class="form-title">
				<text>立即注册</text>
			</view>
			<!-- Phone Input -->
			<view class="form-item">
				<view class="phone-prefix">+86</view>
				<input class="phone-input" type="number" v-model="formData.phone" placeholder="请输入手机号码"
					placeholder-class="placeholder" />
			</view>

			<!-- Verification Code Input -->
			<view class="code-row">
				<view class="form-item">
					<view class="phone-prefix">验证码</view>
					<input class="phone-input" type="number" v-model="formData.code" placeholder="请输入验证码"
						placeholder-class="placeholder" />
				</view>
				<view class="code-btn" :class="{ 'code-btn-disabled': codeTimer > 0 }" @click="getVerifyCode">
					<text>{{ codeTimer > 0 ? `${codeTimer}秒` : '获取验证码' }}</text>
				</view>
			</view>

			<!-- Password Input -->
			<view class="form-item">
				<view class="phone-prefix">密码</view>
				<input class="password-input" type="password" v-model="formData.password" placeholder="请设置6-20位密码"
					placeholder-class="placeholder" style="height: 100rpx;" />
			</view>

			<!-- Confirm Password Input -->
			<view class="form-item">
				<view class="phone-prefix">确认</view>
				<input class="password-input" type="password" v-model="formData.confirmPassword" placeholder="请再次输入密码"
					placeholder-class="placeholder" style="height: 100rpx;" />
			</view>

			<!-- Agreement -->
			<view class="agreement">
				<label class="checkbox-wrapper">
					<checkbox :checked="isAgree" @click="toggleAgreement" color="#1a1a1a" />
				</label>
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link" @click="goToAgreement('userAgreement')">《用户协议》</text>
				<text class="agreement-text">和</text>
				<text class="agreement-link" @click="goToAgreement('privacyPolicy')">《隐私政策》</text>
			</view>

			<view class="login-link">
				已有账号？
				<text @click="goToLogin">登录</text>
			</view>

			<!-- Register Button -->
			<button class="login-btn" @click="handleRegister">注册</button>

			<!-- Back to Home -->
			<view class="home-btn" @click="goToHome">
				<text>返回首页</text>
			</view>
		</view>
	</view>
</template>

<script>
import { register, getSmsCode } from '../../utils/api.js'

export default {
	data() {
		return {
			formData: {
				phone: '',
				code: '',
				password: '',
				confirmPassword: ''
			},
			isAgree: false,
			codeTimer: 0,
			timerInterval: null,
			loading: false
		};
	},
	computed: {
		// 是否可以获取验证码
		canGetCode() {
			return this.isValidPhone(this.formData.phone);
		},
		// 是否可以注册
		canRegister() {
			return this.isValidPhone(this.formData.phone) &&
				this.formData.code &&
				this.isValidPassword(this.formData.password) &&
				this.formData.password === this.formData.confirmPassword &&
				this.isAgree;
		}
	},
	methods: {
		// 验证手机号
		isValidPhone(phone) {
			return /^1[3-9]\d{9}$/.test(phone);
		},

		// 验证密码，6-20位，至少包含数字和字母
		isValidPassword(password) {
			return password && password.length >= 6 && password.length <= 20;
		},

		// 获取验证码
		async getVerifyCode() {
			if (!this.canGetCode || this.codeTimer > 0) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				})
				return;
			}

			try {
				await getSmsCode(this.formData.phone);

				// 提示验证码发送成功
				uni.showToast({
					title: '验证码发送成功',
					icon: 'success'
				});

				// 开始倒计时
				this.codeTimer = 60;
				this.timerInterval = setInterval(() => {
					this.codeTimer--;
					if (this.codeTimer <= 0) {
						clearInterval(this.timerInterval);
					}
				}, 1000);
			} catch (error) {
				uni.showToast({
					title: error.message || '获取验证码失败',
					icon: 'none'
				});
			}
		},

		// 处理注册
		async handleRegister() {
			if (!this.canRegister) {
				uni.showToast({
					title: '请检查输入内容,确保手机号,验证码,密码,确认密码都正确',
					icon: 'none'
				});
				return;
			}

			// 验证表单
			if (!this.isValidPhone(this.formData.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			if (!this.formData.code) {
				uni.showToast({
					title: '请输入验证码',
					icon: 'none'
				});
				return;
			}

			if (!this.isValidPassword(this.formData.password)) {
				uni.showToast({
					title: '密码长度应为6-20位',
					icon: 'none'
				});
				return;
			}

			if (this.formData.password !== this.formData.confirmPassword) {
				uni.showToast({
					title: '两次输入的密码不一致',
					icon: 'none'
				});
				return;
			}

			if (!this.isAgree) {
				uni.showToast({
					title: '请阅读并同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '注册中...'
			})

			try {
				// 构建注册数据
				const registerData = {
					phone: this.formData.phone,
					smsCode: this.formData.code,
					password: this.formData.password
				};

				// 调用注册接口
				const res = await register(registerData);

				// 更新全局登录状态，如果后端直接返回了token
				if (res.token) {
					const app = getApp();
					app.globalData.isLogin = true;
					app.globalData.userInfo = res.userInfo || {};
				}

				setTimeout(() => {
					uni.showToast({
						title: '注册成功,请登录',
						icon: 'success'
					});
				}, 0)

				// 延迟跳转到登录页或首页
				setTimeout(() => {
					if (res.token) {
						// 注册成功且已自动登录，跳转到首页
						uni.redirectTo({
							url: '/pages/index/index'
						});
					} else {
						// 跳转到登录页
						uni.redirectTo({
							url: '/pages/login/login'
						});
					}
				}, 1500);
			} catch (error) {
				uni.showToast({
					title: error.message || '注册失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading()
			}
		},

		toggleAgreement() {
			this.isAgree = !this.isAgree;
		},

		goToLogin() {
			uni.redirectTo({
				url: '/pages/login/login'
			});
		},

		goToAgreement(type) {
			uni.navigateTo({
				url: `/pages/agreement/agreement?type=${type}`
			});
		},

		// 返回首页
		goToHome() {
			uni.redirectTo({
				url: '/pages/index/index'
			})
		}
	},
	onUnload() {
		// 页面卸载时清除定时器
		if (this.timerInterval) {
			clearInterval(this.timerInterval);
		}
	}
};
</script>

<style>
/* Global checkbox styles for all platforms */
checkbox .uni-checkbox-input,
checkbox .wx-checkbox-input {
	width: 48rpx !important;
	height: 48rpx !important;
	border-radius: 4rpx;
}

checkbox .uni-checkbox-input.uni-checkbox-input-checked,
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
	background-color: #1a1a1a !important;
	border-color: #1a1a1a !important;
	color: #fff !important;
	width: 48rpx !important;
	height: 48rpx !important;
}

/* For H5 */
/deep/ .uni-checkbox-input {
	width: 48rpx !important;
	height: 48rpx !important;
}

/deep/ .uni-checkbox-input.uni-checkbox-input-checked {
	background-color: #1a1a1a !important;
	border-color: #1a1a1a !important;
	width: 48rpx !important;
	height: 48rpx !important;
}

/* For App */
::v-deep .uni-checkbox-input {
	width: 48rpx !important;
	height: 48rpx !important;
}

::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
	background-color: #1a1a1a !important;
	border-color: #1a1a1a !important;
	width: 48rpx !important;
	height: 48rpx !important;
}

.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #343332;
	position: relative;
	padding: 0;
	box-sizing: border-box;
	overflow: hidden;
}

.container::before {
	content: '';
	position: absolute;
	top: -100px;
	left: -60px;
	width: 210px;
	height: 210px;
	background: linear-gradient(165deg, #999 0%, #343332 90%);
	border-radius: 0 0 140px 0;
	z-index: 0;
}

.header-container {
	margin-top: 200rpx;
	margin-bottom: 80rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.logo-container {
	margin-bottom: 5rpx;
	width: 200rpx;
	height: 120rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.logo {
	width: 100%;
	height: 100%;
}

.welcome-container {
	padding: 0;
	height: 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-top: 40rpx;
}

.welcome-text {
	font-size: 36rpx;
	color: #ffffff;
	text-align: center;
}

.form-container {
	width: 100%;
	background-color: #ffffff;
	border-top-left-radius: 60rpx;
	border-top-right-radius: 60rpx;
	padding: 40rpx;
	box-sizing: border-box;
	flex: 1;
}

.form-title {
	margin-bottom: 30rpx;
}

.form-title text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.form-item {
	display: flex;
	height: 100rpx;
	border: 1px solid #f2f2f2;
	border-radius: 8rpx;
	align-items: center;
	margin-bottom: 20rpx;
	box-sizing: border-box;
	overflow: hidden;
}

.phone-prefix {
	font-size: 30rpx;
	color: #333333;
	padding: 0 10rpx;
	width: 140rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
}

.phone-input,
.password-input {
	flex: 1;
	height: 100%;
	font-size: 30rpx;
	color: #333333;
	line-height: 100rpx;
	vertical-align: middle;
	border: none;
	outline: none;
}

.phone-input::placeholder,
.password-input::placeholder {
	color: #ccc;
}

.placeholder {
	color: #ccc;
}

.password-input {
	margin-left: 2rpx;
}

.code-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
	width: 100%;
}

.code-row .form-item {
	flex: 1;
	margin-bottom: 0;
}

/* Code button styles */
.code-btn {
	height: 100rpx;
	width: 180rpx;
	background-color: #1a1a1a;
	color: #ffffff;
	font-size: 26rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 20rpx;
	border-radius: 8rpx;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	text-align: center;
}

/* Disabled code button styles */
.code-btn-disabled {
	color: #1a1a1a;
	background-color: #f2f2f2;
}

.login-link {
	display: flex;
	justify-content: center;
	margin-top: 50rpx;
	font-size: 28rpx;
	color: #999;
}

.login-link text {
	color: #d6b391;
}

.agreement {
	display: flex;
	align-items: center;
	margin-top: 30rpx;
	margin-bottom: 20rpx;
	font-size: 24rpx;
	flex-wrap: wrap;
}

.checkbox-wrapper {
	margin-right: 5rpx;
	display: flex;
	align-items: center;
	transform: scale(0.8);
}

.agreement-text {
	color: #666;
	margin: 0 5rpx;
}

.agreement-link {
	color: #d6b391;
}

.login-btn {
	width: 100%;
	height: 90rpx;
	background-color: #1a1a1a;
	color: #ffffff;
	font-size: 34rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60rpx;
}

.home-btn {
	width: 100%;
	text-align: center;
	margin-top: 60rpx;
	padding: 20rpx 0;
}

.home-btn text {
	font-size: 28rpx;
	color: #666666;
}

@media screen and (max-height: 700px) {
	.header-container {
		margin-bottom: 30rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.login-btn {
		margin-top: 30rpx;
		margin-bottom: 40rpx;
	}
}
</style>