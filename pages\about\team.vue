<template>
	<view class="team-container">
		<view class="team-section">
			<view class="section-title">
				<text>管理团队</text>
			</view>
			
			<view class="member-list">
				<view class="member-item">
					<image class="member-avatar" src="https://via.placeholder.com/200x200/3a559f/fff?text=CEO" mode="aspectFill"></image>
					<view class="member-info">
						<text class="member-name">王明</text>
						<text class="member-title">创始人 & CEO</text>
						<text class="member-desc">前某知名电商平台技术副总裁，10年互联网行业经验，5年奢侈品电商经验。致力于将科技与腕表文化相结合，为用户提供极致体验。</text>
					</view>
				</view>
				
				<view class="member-item">
					<image class="member-avatar" src="https://via.placeholder.com/200x200/3a559f/fff?text=CTO" mode="aspectFill"></image>
					<view class="member-info">
						<text class="member-name">李强</text>
						<text class="member-title">联合创始人 & CTO</text>
						<text class="member-desc">前某互联网巨头技术专家，拥有丰富的大型平台研发经验，主导开发多个亿级用户产品。负责腕表世界技术架构与产品创新。</text>
					</view>
				</view>
				
				<view class="member-item">
					<image class="member-avatar" src="https://via.placeholder.com/200x200/3a559f/fff?text=COO" mode="aspectFill"></image>
					<view class="member-info">
						<text class="member-name">张伟</text>
						<text class="member-title">COO</text>
						<text class="member-desc">曾任某国际奢侈品牌中国区运营总监，15年腕表行业从业经验。精通供应链管理与品牌合作，负责腕表世界的整体运营与业务拓展。</text>
					</view>
				</view>
				
				<view class="member-item">
					<image class="member-avatar" src="https://via.placeholder.com/200x200/3a559f/fff?text=CMO" mode="aspectFill"></image>
					<view class="member-info">
						<text class="member-name">陈洁</text>
						<text class="member-title">CMO</text>
						<text class="member-desc">前某知名电商平台营销总监，拥有丰富的数字营销经验。负责腕表世界的品牌建设、市场策略与用户增长。</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="team-section">
			<view class="section-title">
				<text>核心团队</text>
			</view>
			
			<view class="core-team">
				<view class="team-group">
					<view class="group-title">
						<text>产品研发团队</text>
					</view>
					<view class="group-desc">
						<text>由20多位经验丰富的工程师和产品经理组成，专注于产品功能开发、用户体验优化和技术创新。团队成员来自国内外一线互联网公司，拥有扎实的专业背景和丰富的项目经验。</text>
					</view>
				</view>
				
				<view class="team-group">
					<view class="group-title">
						<text>腕表专家团队</text>
					</view>
					<view class="group-desc">
						<text>由10多位资深腕表评鉴师和收藏专家组成，平均从业经验超过10年。团队负责产品评测、内容创作和专业咨询服务，为用户提供专业的腕表知识和购买建议。</text>
					</view>
				</view>
				
				<view class="team-group">
					<view class="group-title">
						<text>客户服务团队</text>
					</view>
					<view class="group-desc">
						<text>由30多位专业客服人员组成，全天候为用户提供在线咨询、购买指导和售后服务。团队成员均接受过专业的腕表知识培训，能够解答用户关于腕表的各类问题。</text>
					</view>
				</view>
				
				<view class="team-group">
					<view class="group-title">
						<text>运营市场团队</text>
					</view>
					<view class="group-desc">
						<text>由15名营销和内容运营专家组成，负责平台活动策划、内容生产和社区运营。团队致力于打造活跃的腕表爱好者社区，为用户提供丰富的互动体验。</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="team-section">
			<view class="section-title">
				<text>企业理念</text>
			</view>
			
			<view class="philosophy-content">
				<text class="philosophy-text">我们相信，每一款腕表都承载着独特的工艺与情感，是时间的艺术品。腕表世界团队致力于传递腕表文化，连接全球腕表爱好者，让精湛工艺与时间艺术得到更多人的欣赏与珍视。</text>
				
				<text class="philosophy-text">我们承诺以专业、真诚的态度服务每一位用户，不断创新产品体验，为腕表爱好者打造一个值得信赖的平台。</text>
				
				<image class="team-image" src="https://via.placeholder.com/700x300/f8f9fc/3a559f?text=Team" mode="aspectFill"></image>
			</view>
		</view>
		
		<view class="join-section">
			<text class="join-title">加入我们</text>
			<text class="join-desc">我们正在寻找热爱腕表、充满激情的伙伴加入腕表世界大家庭</text>
			<button class="join-btn" @click="goToJoinUs">查看职位</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	methods: {
		goToJoinUs() {
			// 实际开发中，这里可以跳转到招聘页面
			uni.showToast({
				title: '招聘功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style>
.team-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 30px;
}

.team-section {
	margin: 15px 15px 0;
	padding: 15px;
	background-color: #fff;
	border-radius: 8px;
}

.section-title {
	margin-bottom: 20px;
	border-left: 4px solid #3a559f;
	padding-left: 10px;
}

.section-title text {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

/* 管理团队样式 */
.member-list {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.member-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 20px;
	border-bottom: 1px dashed #ddd;
}

.member-item:last-child {
	border-bottom: none;
	padding-bottom: 0;
}

.member-avatar {
	width: 120px;
	height: 120px;
	border-radius: 60px;
	margin-bottom: 15px;
}

.member-info {
	text-align: center;
}

.member-name {
	font-size: 18px;
	font-weight: 500;
	color: #333;
	margin-bottom: 5px;
	display: block;
}

.member-title {
	font-size: 16px;
	color: #3a559f;
	margin-bottom: 10px;
	display: block;
}

.member-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	text-align: justify;
	display: block;
}

/* 核心团队样式 */
.core-team {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.team-group {
	padding-bottom: 20px;
	border-bottom: 1px dashed #ddd;
}

.team-group:last-child {
	border-bottom: none;
	padding-bottom: 0;
}

.group-title {
	margin-bottom: 10px;
}

.group-title text {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.group-desc text {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
}

/* 企业理念样式 */
.philosophy-text {
	font-size: 15px;
	color: #666;
	line-height: 1.6;
	margin-bottom: 15px;
	display: block;
	text-align: justify;
}

.team-image {
	width: 100%;
	height: 150px;
	border-radius: 8px;
	margin-top: 20px;
}

/* 加入我们样式 */
.join-section {
	margin: 15px 15px 0;
	padding: 30px 15px;
	background-color: #3a559f;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.join-title {
	font-size: 20px;
	font-weight: 500;
	color: #fff;
	margin-bottom: 10px;
}

.join-desc {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 20px;
}

.join-btn {
	width: 200px;
	height: 40px;
	line-height: 40px;
	background-color: #fff;
	color: #3a559f;
	font-size: 16px;
	font-weight: 500;
	border-radius: 20px;
}
</style> 