<template>
	<view class="m-recorder" v-show="isShow">
		<view class="icon_ m-recorder-gif">
			<image class="img" :src="'https://www.zhida.net/app-resource/icon/sound.gif'" mode="heightFix" v-if="recordTime < 50"></image>
			<view class="color_4a" v-else>{{ 60 - recordTime }}"后将停止录音</view>
		</view>
		<view class="m-recorder-str2" :class="{ m_recorder_str2: isCancel }">
			<view class="m-recorder-text" v-if="isCancel">
				松手取消
			</view>
			<view class="m-recorder-text" v-else>
				上划取消
			</view>
		</view>

	</view>
</template>

<script>
let recordTimeInterval = null;
export default {
	props: {
		value: {
			type: Boolean,
			default: false
		},
		isCancel: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			recordTime: 1,
			isShow: false,
			emojiList: []
		};
	},
	watch: {
		value: {
			handler: function (newV) {
				console.log('m-recorder value changed:', newV);
				this.isShow = newV;
				this.recordTime = 1;
				if (newV) {
					console.log('启动录音组件显示');
					recordTimeInterval = setInterval(() => {
						this.recordTime++;
						if (this.recordTime === 60) {
							this.touchend();
						}
					}, 1000);
				} else {
					clearInterval(recordTimeInterval);
				}
				this.$nextTick(() => {
					let view = uni.createSelectorQuery().select('.m-recorder-str');
					view.boundingClientRect((data) => {
						this.$emit('recorderTop', data);
					}).exec();
				});
			},
			immediate: true
		}
	},
	methods: {
		touchend() {
			this.$emit('touchend');
			this.$emit('input', false);
		}
	}
};
</script>

<style lang="scss" scoped>
.m-recorder {
	position: fixed;
	z-index: 9999;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	overflow: hidden;
	background-color: rgba(0, 0, 0, 0.6);

	.m-recorder-gif {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 99;
		width: 240rpx;
		height: 120rpx;
		box-sizing: border-box;
		padding: 16rpx;
		border-radius: 16rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		.img {
			width: 80%;
			height: 80%;
		}
		.color_4a {
			font-size: 28rpx;
			color: #333;
		}
	}
	.m-recorder-gif::after {
		position: absolute;
		z-index: -1;
		content: '';
		bottom: -10rpx;
		left: calc(50% - 10rpx);
		width: 20rpx;
		height: 20rpx;
		transform: rotate(45deg);
		background-color: #fff;
	}

	.m-recorder-str2 {
		box-sizing: border-box;
		position: absolute;
		top: 58%;
		left: 50%;
		transform: translateX(-50%);
		width: 200rpx;
		padding: 10rpx 20rpx;
		background-color: rgba(50, 50, 50, 0.7);
		border-radius: 30rpx;
		transition: all 0.2s;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
		.m-recorder-text {
			width: 100%;
			text-align: center;
			color: #f5f5f5;
			font-size: 28rpx;
			white-space: nowrap;
		}
	}
	.m_recorder_str2 {
		background-color: #d03e3e;
		.m-recorder-text {
			color: #fff;
		}
	}
}
</style>
