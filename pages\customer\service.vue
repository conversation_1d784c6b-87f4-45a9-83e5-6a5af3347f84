<template>
	<view class="customer-container">
		
		<view class="service-section">
			<view class="section-title">
				<text>常见问题</text>
			</view>
			
			<view class="faq-list">
				<view class="faq-item" v-for="(item, index) in faqList" :key="index" @click="showFAQDetail(item)">
					<view class="faq-content">
						<text class="faq-question">{{ item.question }}</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
			</view>
		</view>
		
		<view class="service-section">
			<view class="section-title">
				<text>联系方式</text>
			</view>
			
			<view class="contact-list">
				<view class="contact-item" @click="makePhoneCall">
					<view class="contact-icon phone-icon">
						<uni-icons type="phone-filled" size="20" color="#fff"></uni-icons>
					</view>
					<view class="contact-info">
						<text class="contact-title">电话客服</text>
						<text class="contact-value">************</text>
						<text class="contact-desc">工作时间：09:00-20:00</text>
					</view>
					<view class="contact-action">
						<text>拨打</text>
					</view>
				</view>
				
				<!-- <view class="contact-item" @click="startOnlineChat">
					<view class="contact-icon chat-icon">
						<uni-icons type="chatbubble-filled" size="20" color="#fff"></uni-icons>
					</view>
					<view class="contact-info">
						<text class="contact-title">在线客服</text>
						<text class="contact-value">在线咨询</text>
						<text class="contact-desc">工作时间：09:00-22:00</text>
					</view>
					<view class="contact-action">
						<text>咨询</text>
					</view>
				</view> -->
				
				<view class="contact-item" @click="sendEmail">
					<view class="contact-icon email-icon">
						<uni-icons type="email-filled" size="20" color="#fff"></uni-icons>
					</view>
					<view class="contact-info">
						<text class="contact-title">邮箱客服</text>
						<text class="contact-value"><EMAIL></text>
						<text class="contact-desc">回复时间：1-2个工作日</text>
					</view>
					<view class="contact-action">
						<text>发送</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="feedback-link" @click="goToFeedback">
			<text>意见反馈</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			faqList: [
				{
					id: 1,
					question: '如何辨别腕表的真伪？',
					answer: '可以通过检查腕表的细节、机芯、包装、防伪码等方面进行辨别。我们所有商品均提供正品保证，并可提供专业鉴定。'
				},
				{
					id: 2,
					question: '当前价格是否是成交时的价格？',
					answer: '正常情况下，成交价格会在行情价格的交易上下浮动，但是行情价格并非最终成交价格。'
				},
				{
					id: 3,
					question: '腕表保修政策是怎样的？',
					answer: '腕表均享有官方保修服务，具体保修条款请查看产品保修卡或与客服咨询。'
				},
				{
					id: 4,
					question: '如何进行退换货？',
					answer: '未佩戴且保持原貌的商品，在收到商品后7天内可申请退换货。奢侈品、定制商品等特殊商品可能有特殊的退换货政策。'
				}
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		// 导航到常见问题页面
		navigateToFAQ() {
			uni.navigateTo({
				url: '/pages/customer/faq'
			});
		},
		// 显示常见问题详情
		showFAQDetail(item) {
			uni.showModal({
				title: item.question,
				content: item.answer,
				showCancel: false
			});
		},
		// 拨打电话
		makePhoneCall() {
			uni.makePhoneCall({
				phoneNumber: '4008888888',
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},
		// 开始在线聊天
		startOnlineChat() {
			// 实际开发中，这里应该跳转到在线客服聊天页面
			uni.showToast({
				title: '正在为您转接在线客服',
				icon: 'none'
			});
			
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/customer/chat'
				});
			}, 1000);
		},
		// 发送邮件
		sendEmail() {
			// 实际开发中，这里可以调用系统的邮件应用
			uni.setClipboardData({
				data: '<EMAIL>',
				success: () => {
					uni.showToast({
						title: '邮箱已复制到剪贴板',
						icon: 'none'
					});
				}
			});
		},
		// 跳转到意见反馈
		goToFeedback() {
			uni.navigateTo({
				url: '/pages/feedback/feedback'
			});
		}
	}
}
</script>

<style>
.customer-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 30px;
}

.nav-header {
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
	background-color: #fff;
	position: relative;
}

.nav-back {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
}

.nav-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	width: 30px;
}

.service-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30px 0;
	background-color: #fff;
}

.service-logo {
	width: 80px;
	height: 80px;
	border-radius: 40px;
	margin-bottom: 15px;
}

.service-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
	margin-bottom: 5px;
}

.service-desc {
	font-size: 14px;
	color: #999;
}

.service-section {
	margin: 15px 10px;
	border-radius: 8px;
	background-color: #fff;
	overflow: hidden;
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.section-title text {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.more-link {
	display: flex;
	align-items: center;
}

.more-link text {
	font-size: 14px;
	color: #3a559f;
	font-weight: normal;
	margin-right: 5px;
}

/* FAQ列表 */
.faq-list {
	padding: 0 15px;
}

.faq-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px 0;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.faq-item:last-child {
	border-bottom: none;
}

.faq-content {
	flex: 1;
	margin-right: 10px;
}

.faq-question {
	font-size: 15px;
	color: #333;
	line-height: 1.4;
}

/* 联系方式列表 */
.contact-list {
	padding: 0 15px;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-icon {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
}

.phone-icon {
	background-color: #3a559f;
}

.chat-icon {
	background-color: #4cd964;
}

.email-icon {
	background-color: #5856d6;
}

.contact-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.contact-title {
	font-size: 16px;
	color: #333;
	margin-bottom: 3px;
}

.contact-value {
	font-size: 14px;
	color: #666;
	margin-bottom: 3px;
}

.contact-desc {
	font-size: 12px;
	color: #999;
}

.contact-action {
	width: 60px;
	height: 30px;
	border-radius: 15px;
	background-color: rgba(58, 85, 159, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-action text {
	font-size: 14px;
	color: #3a559f;
}

/* 反馈链接 */
.feedback-link {
	width: 120px;
	height: 36px;
	margin: 30px auto;
	border-radius: 8px;
	background-color: rgba(58, 85, 159, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.feedback-link text {
	font-size: 14px;
	color: #3a559f;
}
</style> 