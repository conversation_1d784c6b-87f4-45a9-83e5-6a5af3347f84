<template>
	<view class="profile-container">
		<scroll-view class="scroll-content" scroll-y="true">
			<!-- 用户信息区域 -->
			<view class="user-section">
				<view v-if="isLogin" class="user-info hover-effect" @click="handleSettingClick('profile')">
					<image class="avatar" v-if="userInfo.avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<image class="avatar" v-else src="https://www.zhida.net/app-resource/icon/moren.png" mode="aspectFill"></image>
					
					<view class="user-detail">
						<text class="username">{{ userInfo.nickName }}</text>
						<view class="user-edit-hint">
							<text class="edit-text">点击编辑个人资料</text>
							<uni-icons type="right" size="12" color="#999"></uni-icons>
						</view>
					</view>
				</view>
				<view v-else class="user-info">
					<image class="avatar" src="https://www.zhida.net/app-resource/icon/moren.png" mode="aspectFill"></image>
					<view class="user-detail">
						<view class="login-register" @click="goLogin">
							<text class="login-register-text">登录/注册</text>
						</view>
						<view class="quick-actions">
							<text class="quick-action-text">快速查价 / 了解行情 / 体验更多</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 服务选项卡 -->
			<view class="menu-grid">
				<view class="grid-item" @click="handleServiceClick('favorite')">
					<view class="icon-container">
						<image src="https://www.zhida.net/app-resource/icon/h-1.png" mode="aspectFill" style="width: 26px; height: 26px;"></image>
						<!-- <uni-icons type="star" size="32" color="#000"></uni-icons> -->
					</view>
					<text class="grid-text">收藏</text>
				</view>
				<view class="grid-item" @click="handleServiceClick('history')">
					<view class="icon-container">
						<image src="https://www.zhida.net/app-resource/icon/h-2.png" mode="aspectFill" style="width: 26px; height: 26px;"></image>
						<!-- <uni-icons type="eye" size="32" color="#000"></uni-icons> -->
					</view>
					<text class="grid-text">浏览记录</text>
				</view>
				<view class="grid-item" @click="handleServiceClick('message')">
					<view class="icon-container">
						<image src="https://www.zhida.net/app-resource/icon/h-3.png" mode="aspectFill" style="width: 26px; height: 26px;"></image>
						<!-- <uni-icons type="chatbubble" size="32" color="#000"></uni-icons> -->
						<view class="message-dot" v-if="hasNewMessage"></view>
					</view>
					<text class="grid-text">消息</text>
				</view>
			</view>

			<!-- 功能列表区域 -->
			<view class="list-section">
				<!-- 账号安全 -->
				<view class="list-item" @click="handleSettingClick('security')">
					<view class="item-left">
						<!-- <uni-icons type="locked" size="26" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pa.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">账号与安全</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
				
				<!-- 地址管理 -->
				<view class="list-item" @click="handleSettingClick('address')">
					<view class="item-left">
						<!-- <uni-icons type="location" size="20" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pb.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">地址管理</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
				
				<!-- 隐私设置 -->
				<view class="list-item" @click="handleSettingClick('privacy')">
					<view class="item-left">
						<!-- <uni-icons type="eye" size="18" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pc.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">隐私设置</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
				
				<!-- 关于我们 -->
				<view class="list-item" @click="handleSettingClick('about')">
					<view class="item-left">
						<!-- <uni-icons type="info" size="18" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pd.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">关于我们</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
				
			</view>

			<!-- 功能列表区域 -->
			<view class="list-section" style="margin-top: 10px;">
				
				<!-- 意见反馈 -->
				<view class="list-item" @click="handleSettingClick('feedback')">
					<view class="item-left">
						<!-- <uni-icons type="chatboxes" size="18" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pe.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">意见反馈</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
				
				<!-- 联系客服 -->
				<view class="list-item" @click="handleSettingClick('customer')">
					<view class="item-left">
						<!-- <uni-icons type="headphones" size="18" color="#000"></uni-icons> -->
						<image src="https://www.zhida.net/app-resource/icon/pf.png" mode="aspectFill" style="width: 18px; height: 18px;"></image>
						<text class="item-name">联系客服</text>
					</view>
					<uni-icons type="right" size="14" color="#ccc"></uni-icons>
				</view>
			</view>
		</scroll-view>

		<!-- 登录提示横条 -->
		<view v-if="!isLogin && showLoginBar" class="login-bar">
			<view class="login-bar-close" @click="closeLoginBar">
				<uni-icons type="closeempty" size="14" color="#fff"></uni-icons>
			</view>
			<text class="login-bar-text">登录后可探索更多内容</text>
			<view class="login-bar-btn" @click="goLogin">
				<text>去登录</text>
				<uni-icons type="right" size="14" color="#d6b391"></uni-icons>
			</view>
		</view>

		<!-- 固定底部导航栏 -->
		<BottomBar />
	</view>
</template>

<script>
import { getUnreadMessageCount } from '@/utils/api.js';
import BottomBar from '@/components/BottomBar.vue'
export default {
	components: {
		BottomBar
	},
	data() {
		return {
			isLogin: false,
			hasNewMessage: true,
			userInfo: {
				avatar: '',
				nickName: '',
				username: '',
				userId: ''
			},
			showLoginBar: true
		}
	},
	onLoad() {
		// 页面加载时的初始化
	},
	onShow() {
		console.log('onShow');
		// 获取版本号
		const system = uni.getSystemInfoSync();
		this.version = system.appWgtVersion

		// 每次显示页面时检查登录状态
		this.checkLoginStatus();
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const app = getApp();
			this.isLogin = app.globalData.isLogin;

			if (this.isLogin) {
				// 获取用户信息，实际开发中应该从全局状态或本地存储获取
				this.userInfo = app.globalData.userInfo

				// 获取未读消息数量
				this.getUnreadMessageCount();
			}
		},
		// 获取未读消息数量
		getUnreadMessageCount() {
			getUnreadMessageCount().then(res => {
				console.log(res);
				this.hasNewMessage = res.data > 0;
			});
		},
		// 跳转到登录页
		goLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		// 处理服务项点击
		handleServiceClick(id) {
			const serviceRoutes = {
				'favorite': '/pages/favorite/favorite',
				'history': '/pages/history/history',
				'message': '/pages/message/list'
			};
			
			if (serviceRoutes[id]) {
				uni.navigateTo({ url: serviceRoutes[id] });
			}
		},
		// 处理设置项点击
		handleSettingClick(id) {
			const settingRoutes = {
				'profile': '/pages/profile/edit',
				'security': '/pages/security/security',
				'notification': '/pages/notification/settings',
				'address': '/pages/address/list',
				'feedback': '/pages/feedback/feedback',
				'customer': '/pages/customer/service',
				'privacy': '/pages/privacy/settings',
				'about': '/pages/about/about'
			};
			
			if (settingRoutes[id]) {
				uni.navigateTo({ url: settingRoutes[id] });
			}
		},
		// 关闭登录提示条
		closeLoginBar() {
			this.showLoginBar = false;
		},
		// 退出登录
		logout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						const app = getApp();
						app.globalData.isLogin = false;

						// 清除用户数据
						this.isLogin = false;
						this.userInfo = {};
						uni.removeStorageSync('token');
						uni.removeStorageSync('userInfo');

						// 显示提示
						uni.showToast({
							title: '已退出登录',
							icon: 'none'
						});
					}
				}
			});
		}
	}
}
</script>

<style>
.profile-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	position: relative;
}

.scroll-content {
	height: 100vh;
	padding-bottom: 50px;
}

/* 用户信息区域 */
.user-section {
	padding: 20px 15px;
	background-color: #fff;
}

.user-info {
	display: flex;
	align-items: center;
	position: relative;
}

.hover-effect {
	transition: all 0.2s ease;
}

.hover-effect:active {
	opacity: 0.8;
	transform: scale(0.98);
}

.avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
}

.user-detail {
	flex: 1;
	margin-left: 15px;
	display: flex;
	flex-direction: column;
}

.username {
	font-size: 20px;
	font-weight: 600;
	color: #333;
	line-height: 1.2;
	margin-bottom: 8px;
}

.user-edit-hint {
	display: flex;
	align-items: center;
	margin-top: 0;
	padding: 0;
	width: 100%;
}

.edit-text {
	font-size: 12px;
	color: #999;
	margin-right: 2px;
	line-height: 1;
}

.login-register {
	margin-bottom: 4px;
}

.login-register-text {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}


.quick-action-text {
	font-size: 14px;
	color: #d6b391;
}

/* 服务选项卡 */
.menu-grid {
	display: flex;
	background-color: #fff;
	padding: 20px 0;
	margin-bottom: 10px;
}

.grid-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6px;
	position: relative;
	transition: all 0.2s ease;
	position: relative;
}

.grid-item::after {
	content: '';
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 1px;
	height: 30px;  /* 设置分隔线高度 */
	background-color: #eee;
}

.grid-item:last-child::after {
	display: none;  /* 隐藏最后一个元素的分隔线 */
}

.grid-item:active {
	opacity: 0.7;
	transform: scale(0.95);
}

.icon-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 50px;
	height: 32px;
}

.message-dot {
	position: absolute;
	top: 0;
	right: 0;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background-color: #ff5252;
}

.grid-text {
	font-size: 15px;
	color: #333;
}

/* 功能列表区域 */
.list-section {
	padding-top: 4px;
	background-color: #fff;
	overflow: hidden;
}

.list-item {
	height: 60px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 15px;
	transition: all 0.2s ease;
}

.list-item:active {
	background-color: #f9f9f9;
}

.list-item:last-child {
	border-bottom: none;
}

.item-left {
	display: flex;
	align-items: center;
	gap: 8px;
}

.item-name {
	font-size: 15px;
	color: #333;
}

/* 登录提示横条 */
.login-bar {
	position: fixed;
	bottom: 70px;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	height: 56px;
	background-color: rgba(26, 26, 26, 0.95);
	color: #fff;
	display: flex;
	align-items: center;
	padding: 0 10px;
	z-index: 99;
	box-sizing: border-box;
	border-radius: 3px;
	box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.login-bar-close {
	width: 28px;
	height: 28px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.login-bar-text {
	flex: 1;
	font-size: 15px;
	margin-left: 2px;
}

.login-bar-btn {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #d6b391;
	gap: 4px;
	border-radius: 6px;
	transition: all 0.2s ease;
}

.login-bar-btn:active {
	transform: scale(0.95);
	opacity: 0.9;
}
</style>