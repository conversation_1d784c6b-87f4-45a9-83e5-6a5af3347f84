<template>
	<view class="container">
		<!-- <PERSON><PERSON> and Header -->
		<view class="header-container">
			<view class="logo-container">
				<image class="logo" src="https://www.zhida.net/app-resource/icon/w-logo.png" mode="aspectFit"></image>
			</view>
			<view class="welcome-container">
				<text class="welcome-text">Hi~ 欢迎来到值达!</text>
			</view>
		</view>
		
		<!-- Reset Form -->
		<view class="form-container">
			<view class="form-title">
				<text>重置密码</text>
			</view>
			
			<!-- Phone Input -->
			<view class="form-item">
				<view class="phone-prefix">手机号</view>
				<input class="phone-input" type="number" v-model="formData.phone" placeholder="请输入手机号码" placeholder-class="placeholder" />
			</view>
			
			<!-- Verification Code -->
			<view class="code-row">
				<view class="form-item">
					<view class="phone-prefix">验证码</view>
					<input class="phone-input" type="number" v-model="formData.code" placeholder="请输入验证码" placeholder-class="placeholder" />
				</view>
				<view class="code-btn" :class="{ 'code-btn-disabled': counting }" @click="handleGetVerificationCode">
					<text>{{ counting ? `${countDown}秒` : '获取验证码' }}</text>
				</view>
			</view>
			
			<!-- New Password -->
			<view class="form-item">
				<view class="phone-prefix">新密码</view>
				<input class="password-input" type="password" v-model="formData.password" placeholder="请输入新密码" placeholder-class="placeholder" style="height: 100rpx;" />
			</view>
			
			<!-- Confirm Password -->
			<view class="form-item">
				<view class="phone-prefix">确认密码</view>
				<input class="password-input" type="password" v-model="formData.confirmPassword" placeholder="请再次输入新密码" placeholder-class="placeholder" style="height: 100rpx;" />
			</view>

			<view class="login-link">
				已有账号？
				<text @click="goToLogin">登录</text>
			</view>
			
			<!-- Reset Button -->
			<button class="login-btn" @click="handleResetPassword">重置密码</button>

			<!-- Back to Home -->
			<view class="home-btn" @click="goToHome">
				<text>返回首页</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getSmsCode, forgotPassword } from '@/utils/api.js'

export default {
	data() {
		return {
			formData: {
				phone: '',
				code: '',
				password: '',
				confirmPassword: ''
			},
			counting: false,
			countDown: 60
		};
	},
	methods: {
		// 处理获取验证码按钮点击
		handleGetVerificationCode() {
			// 如果正在倒计时，不执行任何操作
			if (this.counting) return;
			
			// 调用获取验证码的方法
			this.getVerificationCode();
		},
		
		// 获取验证码
		async getVerificationCode() {
			// 验证手机号
			if (!this.formData.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				})
				return
			}
			
			// 验证手机号格式
			if (!/^1\d{10}$/.test(this.formData.phone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				})
				return
			}
			
			uni.showLoading({
				title: '发送中...'
			})
			
			try {
				// 调用发送验证码API
				await getSmsCode(this.formData.phone)
				
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})
				
				// 开始倒计时
				this.counting = true
				this.countDown = 60
				
				const timer = setInterval(() => {
					this.countDown--
					if (this.countDown <= 0) {
						clearInterval(timer)
						this.counting = false
					}
				}, 1000)
				
			} catch (error) {
				uni.showToast({
					title: error.message || '发送验证码失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 重置密码
		async handleResetPassword() {
			// 表单验证
			if (!this.formData.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				})
				return
			}
			
			if (!this.formData.code) {
				uni.showToast({
					title: '请输入验证码',
					icon: 'none'
				})
				return
			}
			
			if (!this.formData.password) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				})
				return
			}
			
			if (this.formData.password.length < 6) {
				uni.showToast({
					title: '密码长度不能少于6位',
					icon: 'none'
				})
				return
			}
			
			if (this.formData.password !== this.formData.confirmPassword) {
				uni.showToast({
					title: '两次输入的密码不一致',
					icon: 'none'
				})
				return
			}
			
			uni.showLoading({
				title: '重置中...'
			})
			
			try {
				// 调用忘记密码API
				const data = {
					phone: this.formData.phone,
					smsCode: this.formData.code,
					password: this.formData.password
				}
				
				await forgotPassword(data)
				
				uni.hideLoading()
				
				uni.showToast({
					title: '密码重置成功',
					icon: 'success'
				})
				
				// 2秒后跳转回登录页
				setTimeout(() => {
					this.goToLogin()
				}, 2000)
			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: error.message || '密码重置失败',
					icon: 'none'
				})
			}
		},
		
		// 返回登录页
		goToLogin() {
			uni.redirectTo({
				url: '/pages/login/login'
			})
		},
		
		// 返回首页
		goToHome() {
			uni.redirectTo({
				url: '/pages/index/index'
			})
		}
	}
};
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #343332;
	position: relative;
	padding: 0;
	box-sizing: border-box;
	overflow: hidden;
}

.container::before {
	content: '';
	position: absolute;
	top: -100px;
	left: -60px;
	width: 210px;
	height: 210px;
	background: linear-gradient(165deg, #999 0%, #343332 90%);
	border-radius: 0 0 140px 0;
	z-index: 0;
}

.header-container {
	margin-top: 200rpx;
	margin-bottom: 80rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.logo-container {
	margin-bottom: 5rpx;
	width: 200rpx;
	height: 120rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.logo {
	width: 100%;
	height: 100%;
}

.welcome-container {
	padding: 0;
	height: 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-top: 40rpx;
}
.welcome-text {
	font-size: 36rpx;
	color: #ffffff;
	text-align: center;
}

.form-container {
	width: 100%;
	background-color: #ffffff;
	border-top-left-radius: 60rpx;
	border-top-right-radius: 60rpx;
	padding: 40rpx;
	box-sizing: border-box;
	flex: 1;
}

.form-title {
	margin-bottom: 30rpx;
}

.form-title text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.form-item {
	display: flex;
	height: 100rpx;
	border: 1px solid #f2f2f2;
	border-radius: 8rpx;
	align-items: center;
	margin-bottom: 20rpx;
	box-sizing: border-box;
}

.phone-prefix {
	font-size: 30rpx;
	color: #333333;
	padding: 0 10rpx;
	width: 140rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
}

.phone-input, .password-input {
	flex: 1;
	height: 100%;
	font-size: 30rpx;
	color: #333333;
	line-height: 100rpx;
	vertical-align: middle;
	border: none;
	outline: none;
}

.phone-input::placeholder, .password-input::placeholder {
    color: #ccc;
}

.placeholder {
    color: #ccc;
}

.password-input {
	margin-left: 2rpx;
}

.password-input {
	margin-left: 2rpx;
}

.code-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
	width: 100%;
}

.code-row .form-item {
	flex: 1;
	margin-bottom: 0;
}

/* Code button styles */
.code-btn {
	height: 100rpx;
	width: 180rpx;
	background-color: #1a1a1a;
	color: #ffffff;
	font-size: 26rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 20rpx;
	border-radius: 8rpx;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	text-align: center;
}

/* Disabled code button styles */
.code-btn-disabled {
	color: #1a1a1a;
	background-color: #f2f2f2;
}

.login-btn {
	width: 100%;
	height: 90rpx;
	background-color: #1a1a1a;
	color: #ffffff;
	font-size: 34rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60rpx;
}

.forgot-password {
	display: flex;
	justify-content: center;
	margin-top: 30rpx;
}

.forgot-password text {
	font-size: 28rpx;
	color: #999999;
	text-align: center;
}

.login-link {
	display: flex;
	margin-top: 30rpx;
	font-size: 28rpx;
	color: #999;
}

.login-link text {
	color: #d6b391;
}

.home-btn {
	width: 100%;
	text-align: center;
	margin-top: 60rpx;
	padding: 20rpx 0;
}

.home-btn text {
	font-size: 28rpx;
	color: #666666;
}

@media screen and (max-height: 700px) {
	.header-container {
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.login-btn {
		margin-top: 30rpx;
		margin-bottom: 40rpx;
	}
}
</style>