<script>
import { login, getUserInfoByToken, checkForUpdate, downloadUpdate } from './utils/api.js'
import { initRouteInterceptor, initRequestInterceptor } from './utils/permission.js'

export default {
	globalData: {
		isLogin: false,
		userInfo: null,
		timer: null,
		productId: null,
		productDetailStayTime: 0
	},
	onLaunch: function () {
		console.log('App Launch')

		// 检查登录状态
		this.checkLoginStatus()
		// 初始化路由拦截器
		initRouteInterceptor()
		// 初始化API请求拦截器
		initRequestInterceptor()

		// 检查应用更新
		// #ifdef APP-PLUS
		this.checkAppUpdate()
		// #endif

		// 启动定时器
		// this.startTimer()
	},
	onShow: function () {
		console.log('App Show')

		// 如果定时器不存在，重新启动
		// if (!this.globalData.timer) {
		// 	this.startTimer()
		// }
	},
	onHide: function () {
		console.log('App Hide')

		// 应用进入后台时，清除定时器
		this.clearTimer()
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			try {
				// 从本地存储获取token
				const token = uni.getStorageSync('token')
				const userInfo = uni.getStorageSync('userInfo')

				if (token) {
					// 已登录状态
					this.globalData.isLogin = true
					this.globalData.userInfo = userInfo
					console.log('用户已登录')
				} else {
					// 未登录状态
					this.globalData.isLogin = false
					this.globalData.userInfo = null
					console.log('用户未登录')
				}
			} catch (e) {
				console.error('检查登录状态失败', e)
			}
		},

		// 启动状态检查定时器
		startTimer() {
			// 如果已经存在定时器，先清除
			this.clearTimer()

			this.globalData.timer = setInterval(() => {
				// 请求接口
				const token = uni.getStorageSync('token')
				if (token) {
					// console.log('token存在');
				}
			}, 3000)

			console.log('状态检查定时器已启动')
		},

		// 清除定时器
		clearTimer() {
			if (this.globalData.timer) {
				clearInterval(this.globalData.timer)
				this.globalData.timer = null
				console.log('状态检查定时器已清除')
			}
		},

		// 全局登录方法
		doLogin(callback) {
			uni.showToast({
				title: '登录中...',
				icon: 'loading',
				duration: 2000
			});
			// 跳转到登录页面
			uni.switchTab({
				url: '/pages/login/index',
				success: () => {
					if (typeof callback === 'function') {
						callback()
					}
				}
			})
		},

		// 检查应用更新
		checkAppUpdate() {
			console.log('检查应用更新...')
			checkForUpdate().then(res => {
				const system = uni.getSystemInfoSync();
				const currentVersion = system.appWgtVersion;
				const serverVersion = res.data.version;
				console.log(res.data, '当前版本:' + currentVersion, '服务器版本:' + serverVersion)

				// 版本号不一致，则提示更新
				if (serverVersion !== currentVersion) {
					this.showUpdateConfirm(res.data)
				} else {
					console.log('当前已是最新版本')
				}

			}).catch(err => {
				console.log('检查更新失败', err)
			})
		},

		// 显示更新确认弹窗
		showUpdateConfirm(updateInfo) {
			uni.showModal({
				title: '发现新版本',
				content: updateInfo.description || '有新版本可用，是否立即更新？',
				confirmText: '立即更新',
				cancelText: '稍后再说',
				success: (res) => {
					if (res.confirm) {
						console.log(updateInfo.downloadUrl)
						this.doUpdate(updateInfo.downloadUrl)
					} else {
						console.log('用户取消更新')
					}
				}
			})
		},

		// 执行更新
		doUpdate(downloadUrl) {
			uni.showLoading({
				title: '下载更新中...'
			});

			// 不同平台更新方式不同
			// #ifdef APP-PLUS
			downloadUpdate(downloadUrl).then(tempFilePath => {
				console.log('下载成功', tempFilePath)
				// 安装更新
				plus.runtime.install(
					tempFilePath,
					{ force: false },
					() => {
						uni.hideLoading();
						console.log('安装成功，重启应用')
						plus.runtime.restart()
					},
					(e) => {
						uni.hideLoading();
						console.log('安装失败', e)
						uni.showToast({
							title: '安装更新失败',
							icon: 'none'
						})
					}
				)
			}).catch(err => {
				console.log('下载失败', err)
				uni.showToast({
					title: '下载失败',
					icon: 'none'
				})
			})
			// #endif

			// #ifdef H5 || MP
			// 小程序和H5环境下，直接提示刷新页面
			uni.showModal({
				title: '更新提示',
				content: '更新已就绪，请刷新页面',
				showCancel: false,
				success: () => {
					// H5下可以刷新页面
					// #ifdef H5
					location.reload()
					// #endif
				}
			})
			// #endif
		}
	}
}
</script>

<style>
/* #ifdef APP-PLUS */
@font-face {
	font-family: 'SourceHanSans';
	src: url('https://www.zhida.net/app-resource/SourceHanSansCN-Normal.otf') format('truetype');
	font-weight: normal;
	font-style: normal;
}

/* #endif */

/* TabBar相关样式 */
.tab-bar-placeholder {
	height: 56px;
	/* 与tabBar高度一致 */
}

/* 防止内容被tabBar遮挡 */
.content-with-tab-bar {
	padding-bottom: 56px;
}

/* 任何滚动容器都不显示滚动条 */
::-webkit-scrollbar {
	width: 0 !important;
	height: 0 !important;
	display: none;
}

/* #ifdef APP-PLUS */
.safe-area-inset-bottom {
	padding-bottom: env(safe-area-inset-bottom);
}

/* #endif */
</style>
