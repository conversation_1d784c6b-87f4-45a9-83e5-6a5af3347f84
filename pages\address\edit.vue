<template>
	<view class="address-edit-container">
		<view class="form-section">
			<view class="form-item">
				<text class="item-label">收货人</text>
				<input class="item-input" type="text" v-model="addressInfo.name" placeholder="请输入收货人姓名" />
			</view>
			<view class="form-item">
				<text class="item-label">手机号码</text>
				<input class="item-input" type="number" v-model="addressInfo.phone" placeholder="请输入收货人手机号" />
			</view>
			<view class="form-item address-area">
				<text class="item-label">所在地区</text>
				<view class="picker-container">
					<!-- 省份选择 -->
					<picker class="picker-item" :range="provinceList" range-key="name" :value="provinceIndex" @change="provinceChange">
						<view class="picker-text">{{ addressInfo.province || '选择省份' }}</view>
					</picker>
					<!-- 城市选择 -->
					<picker class="picker-item" :range="cityList" range-key="name" :value="cityIndex" @change="cityChange" :disabled="!addressInfo.province">
						<view class="picker-text">{{ addressInfo.city || '选择城市' }}</view>
					</picker>
					<!-- 区县选择 -->
					<picker class="picker-item" :range="countyList" range-key="name" :value="countyIndex" @change="countyChange" :disabled="!addressInfo.city">
						<view class="picker-text">{{ addressInfo.district || '区/县' }}</view>
					</picker>
				</view>
			</view>
			<view class="form-item textarea-item">
				<text class="item-label">详细地址</text>
				<input class="item-input" type="text" v-model="addressInfo.address" placeholder="请输入详细地址，如道路、门牌号、小区、楼栋号、单元等" />
			</view>
			<view class="form-item switch-item">
				<text class="item-label">设为默认地址</text>
				<switch :checked="addressInfo.isDefault" color="#1a1a1a" @change="setDefaultChange" />
			</view>
		</view>
		
		<button class="save-btn" @click="saveAddress">保存</button>
		
		<view v-if="addressId" class="delete-btn" @click="deleteAddress">
			<text>删除该地址</text>
		</view>
	</view>
</template>

<script>
import { area } from '@/utils/area.js';
import { saveAddress, getAddressDetail, deleteAddress } from '@/utils/api.js';

export default {
	data() {
		return {
			addressId: null,
			addressInfo: {
				name: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				address: '',
				isDefault: 0
			},
			// 省市区选择器数据
			provinceList: [],
			cityList: [],
			countyList: [],
			provinceIndex: 0,
			cityIndex: 0,
			countyIndex: 0,
			// 省市区编码
			currentProvince: '',
			currentCity: '',
			currentCounty: '',
			// 是否正在提交
			submitting: false
		}
	},
	created() {
		// 初始化省份数据
		this.initProvinceList();
	},
	onLoad(options) {
		if (options.id) {
			this.addressId = parseInt(options.id);
			// 获取地址详情
			this.getAddressInfo();
		}
	},
	methods: {
		// 获取地址详情
		getAddressInfo() {
			uni.showLoading({
				title: '加载中...'
			});
			
			getAddressDetail(this.addressId).then(res => {
				uni.hideLoading();
				
				if (res.code === 200 && res.data) {
					this.addressInfo = res.data;
					// 根据当前地址找到对应的省市区编码
					this.findAreaCode();
				} else {
					this.showToast('获取地址详情失败');
				}
			}).catch(err => {
				uni.hideLoading();
				this.showToast(err.message || '获取地址详情失败');
			});
		},
		
		// 初始化省份列表
		initProvinceList() {
			const provinceList = [];
			for (const provinceCode in area.province_list) {
				provinceList.push({
					code: provinceCode,
					name: area.province_list[provinceCode]
				});
			}
			this.provinceList = provinceList;
		},
		
		// 根据省份编码获取城市列表
		updateCityList(provinceCode) {
			const cityList = [];
			// 城市编码前两位等于省份编码前两位
			const provincePrefix = provinceCode.substring(0, 2);
			
			for (const cityCode in area.city_list) {
				if (cityCode.substring(0, 2) === provincePrefix) {
					cityList.push({
						code: cityCode,
						name: area.city_list[cityCode]
					});
				}
			}
			this.cityList = cityList;
		},
		
		// 根据城市编码获取区县列表
		updateCountyList(cityCode) {
			const countyList = [];
			// 区县编码前四位等于城市编码前四位
			const cityPrefix = cityCode.substring(0, 4);
			
			for (const countyCode in area.county_list) {
				if (countyCode.substring(0, 4) === cityPrefix) {
					countyList.push({
						code: countyCode,
						name: area.county_list[countyCode]
					});
				}
			}
			this.countyList = countyList;
		},
		
		// 根据已有的地址信息找到对应的省市区编码
		findAreaCode() {
			const { province, city, district } = this.addressInfo;
			
			// 查找省份编码
			for (const provinceCode in area.province_list) {
				if (area.province_list[provinceCode] === province) {
					this.currentProvince = provinceCode;
					this.updateCityList(provinceCode);
					break;
				}
			}
			
			// 查找城市编码
			if (this.currentProvince) {
				for (const cityCode in area.city_list) {
					if (area.city_list[cityCode] === city && cityCode.substring(0, 2) === this.currentProvince.substring(0, 2)) {
						this.currentCity = cityCode;
						this.updateCountyList(cityCode);
						break;
					}
				}
			}
			
			// 查找区县编码
			if (this.currentCity) {
				for (const countyCode in area.county_list) {
					if (area.county_list[countyCode] === district && countyCode.substring(0, 4) === this.currentCity.substring(0, 4)) {
						this.currentCounty = countyCode;
						break;
					}
				}
			}
			
			// 设置选择器索引
			if (this.currentProvince) {
				this.provinceIndex = this.provinceList.findIndex(item => item.code === this.currentProvince);
			}
			
			if (this.currentCity) {
				this.cityIndex = this.cityList.findIndex(item => item.code === this.currentCity);
			}
			
			if (this.currentCounty) {
				this.countyIndex = this.countyList.findIndex(item => item.code === this.currentCounty);
			}
		},
		
		// 省份选择变化
		provinceChange(e) {
			const index = e.detail.value;
			const selectedProvince = this.provinceList[index];
			
			this.provinceIndex = index;
			this.currentProvince = selectedProvince.code;
			this.addressInfo.province = selectedProvince.name;
			
			// 更新城市列表
			this.updateCityList(selectedProvince.code);
			
			// 重置城市和区县选择
			this.currentCity = '';
			this.currentCounty = '';
			this.addressInfo.city = '';
			this.addressInfo.district = '';
			this.cityIndex = 0;
			this.countyIndex = 0;
			this.countyList = [];
		},
		
		// 城市选择变化
		cityChange(e) {
			const index = e.detail.value;
			const selectedCity = this.cityList[index];
			
			this.cityIndex = index;
			this.currentCity = selectedCity.code;
			this.addressInfo.city = selectedCity.name;
			
			// 更新区县列表
			this.updateCountyList(selectedCity.code);
			
			// 重置区县选择
			this.currentCounty = '';
			this.addressInfo.district = '';
			this.countyIndex = 0;
		},
		
		// 区县选择变化
		countyChange(e) {
			const index = e.detail.value;
			const selectedCounty = this.countyList[index];
			
			this.countyIndex = index;
			this.currentCounty = selectedCounty.code;
			this.addressInfo.district = selectedCounty.name;
		},
		
		// 设置默认地址
		setDefaultChange(e) {
			this.addressInfo.isDefault = e.detail.value;
		},
		
		// 保存地址
		saveAddress() {
			// 防止重复提交
			if (this.submitting) {
				return;
			}
			
			// 表单验证
			if (!this.addressInfo.name) {
				this.showToast('请输入收货人姓名');
				return;
			}
			
			if (!this.addressInfo.phone) {
				this.showToast('请输入手机号码');
				return;
			}
			
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(this.addressInfo.phone)) {
				this.showToast('请输入正确的手机号码');
				return;
			}
			
			if (!this.addressInfo.province || !this.addressInfo.city || !this.addressInfo.district) {
				this.showToast('请选择所在地区');
				return;
			}
			
			if (!this.addressInfo.address) {
				this.showToast('请输入详细地址');
				return;
			}
			
			// 显示保存中
			this.submitting = true;
			uni.showLoading({
				title: '保存中...'
			});
			
			// 准备要提交的数据
			const submitData = {
				...this.addressInfo
			};
			
			// 如果是编辑模式，需要传入ID
			if (this.addressId) {
				submitData.id = this.addressId;
			}

			submitData.isDefault = submitData.isDefault ? 1 : 0;
			
			// 调用API保存地址
			saveAddress(submitData).then(res => {
				this.submitting = false;
				uni.hideLoading();
				
				if (res.code === 200) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					this.showToast(res.message || '保存失败，请重试');
				}
			}).catch(err => {
				this.submitting = false;
				uni.hideLoading();
				this.showToast(err.message || '保存失败，请重试');
			});
		},
		
		// 删除地址
		deleteAddress() {
			if (!this.addressId) {
				return;
			}
			
			uni.showModal({
				title: '提示',
				content: '确定要删除该地址吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '删除中...'
						});
						
						deleteAddress(this.addressId).then(res => {
							uni.hideLoading();
							
							if (res.code === 200) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								
								// 返回上一页
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							} else {
								this.showToast(res.message || '删除失败，请重试');
							}
						}).catch(err => {
							uni.hideLoading();
							this.showToast(err.message || '删除失败，请重试');
						});
					}
				}
			});
		},
		
		// 显示提示
		showToast(title) {
			uni.showToast({
				title: title,
				icon: 'none'
			});
		}
	}
}
</script>

<style>
.address-edit-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 40px;
}

.form-section {
	margin-top: 10px;
	background-color: #fff;
}

.form-item {
	display: flex;
	align-items: center;
	min-height: 56px;
	padding: 10px 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
	box-sizing: border-box;
}

.form-item:last-child {
	border-bottom: none;
}

.item-label {
	width: 90px;
	font-size: 16px;
	color: #333;
	padding-top: 0;
}

.item-input {
	flex: 1;
	height: 40px;
	font-size: 16px;
}

.address-area {
	align-items: center;
}

.address-area .item-label {
	align-self: center;
}

.picker-container {
	flex: 1;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	margin: 0 -2px;
}

.picker-item {
	flex: 1;
	min-width: 30%;
}

.picker-text {
	font-size: 14px;
	color: #333;
	height: 36px;
	line-height: 36px;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	border-radius: 4px;
	margin: 0 2px;
	padding: 0 5px;
	box-sizing: border-box;
	background-color: #f8f9fc;
}

.form-item.textarea-item {
	align-items: center;
}

.textarea-item .item-label {
	padding-top: 0;
}

.switch-item {
	justify-content: space-between;
}

.save-btn {
	width: calc(100% - 40px);
	height: 46px;
	line-height: 46px;
	margin: 30px 20px;
	font-size: 16px;
	color: #fff;
	background-color: #1a1a1a;
	border-radius: 6px;
	border: none;
	text-align: center;
}

.delete-btn {
	width: calc(100% - 40px);
	height: 46px;
	line-height: 46px;
	margin: 10px 20px 30px;
	font-size: 16px;
	color: #ff3b30;
	background-color: #fff;
	border-radius: 6px;
	text-align: center;
}
</style> 