<template>
    <view class="bottom-bar">
        <!-- 左侧tab：首页 -->
        <view class="tab-item" :class="{ active: currentPath === 'pages/index/index' }" @click="goTab('pages/index/index')">
            <image class="icon" :src="currentPath === 'pages/index/index' ? '/static/tabbar/index_active.png' : '/static/tabbar/index.png'" />
            <text>首页</text>
        </view>

        <!-- 中间大按钮 -->
        <view class="mid-button" @click="goTab('pages/message/message')">
            <image class="mid-icon" src="/static/tabbar/center.png" />
        </view>

        <!-- 右侧tab：我的 -->
        <view class="tab-item" :class="{ active: currentPath === 'pages/profile/profile' }" @click="goTab('pages/profile/profile')">
            <image class="icon" :src="currentPath === 'pages/profile/profile' ? '/static/tabbar/my_active.png' : '/static/tabbar/my.png'" />
            <text>我的</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentPath: ''
        }
    },
    mounted() {
        this.updateCurrentPath()
    },
    methods: {
        updateCurrentPath() {
            const pages = getCurrentPages()
            if (pages.length > 0) {
                this.currentPath = pages[pages.length - 1].route
            }
        },
        goTab(path) {
            // 如果点击的是当前页面，则不进行跳转
            if (this.currentPath === path) {
                console.log('已经在当前页面，无需跳转')
                return
            }
            if (path === 'pages/profile/profile' || path === 'pages/index/index') {
                uni.redirectTo({ url: '/' + path })
            } else {
                uni.navigateTo({ url: '/' + path })
            }
        }
    }
}
</script>

<style scoped>
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: #ffffff;
    border-top: 1px solid #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 9999;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
    padding-bottom: calc(env(safe-area-inset-bottom));
}

/* 普通 tab 样式 */
.tab-item {
    flex: 1;
    text-align: center;
    font-size: 13px;
    color: #1A1A1A;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.tab-item.active {
    color: #d6b391;
}

.tab-item .icon {
    width: 18px;
    height: 18px;
    margin-bottom: 2px;
}

/* 中间按钮 */
.mid-button {
    position: relative;
    top: -2px;
    width: 80px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mid-button .mid-icon {
    height: 32px;
}
</style>