<template>
	<view class="history-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav">
			<view class="nav-header">
				<view class="back-btn" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="page-title">浏览记录</view>
				<view class="layout-toggle" @click="toggleLayout">
					<text>{{ isGridLayout ? '单列' : '多列' }}</text>
					<view class="layout-icon">
						<view v-if="isGridLayout" class="list-icon">
							<!-- <view class="list-line"></view>
							<view class="list-line"></view>
							<view class="list-line"></view> -->
							<image src="https://www.zhida.net/app-resource/icon/duolie.png" style="width: 12px; height: 12px;"></image>
						</view>
						<view v-else class="grid-icon">
							<!-- <view class="grid-dot"></view>
							<view class="grid-dot"></view>
							<view class="grid-dot"></view>
							<view class="grid-dot"></view> -->
							<image src="https://www.zhida.net/app-resource/icon/danlie.png" style="width: 12px; height: 12px;"></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品列表 -->
		<scroll-view class="content-scroll" scroll-y="true" @scrolltolower="loadMore">
			<view class="products-section">
				<view v-if="historyList.length > 0"
					:class="{ 'products-grid': isGridLayout, 'products-list': !isGridLayout }">
					<!-- 多列视图 -->
					<view class="product-item-grid" v-for="(item, index) in historyList" :key="index"
						v-if="isGridLayout" @click="gotoDetail(item.id)">
						<view class="product-image-wrapper">
							<image class="product-image" :src="item.watchesDto.imageMain" mode="aspectFit"></image>
						</view>
						<view class="product-info">
							<view class="product-title">
								<text class="product-name">{{ item.watchesDto.brand }} {{ item.watchesDto.series
									}}</text>
							</view>
							<view class="product-code">
								{{ item.watchesDto.referenceNumber }}
								<view class="copy-btn" @click.stop @click="copyReferenceNumber(item.watchesDto.referenceNumber)"
									style="margin-left: 10px;">
									<image src="https://www.zhida.net/app-resource/icon/1624.png" style="width: 12px; height: 12px;"
										mode="aspectFit"></image>
								</view>
							</view>
							<view class="product-rating">
								<uni-icons type="star" size="16" color="#999"></uni-icons>
								<text class="rating-score">{{ item.watchesDto.like_num || 0 }}</text>
							</view>
						</view>
						<view class="product-price-container">
							<view class="product-price-row">
								<view class="price-box market-price-box">
									<view class="price-content">
										<view class="price-header">
											<text class="price-label">行情价</text>
											<view class="price-icon-inline">
												<image v-if="item.watchesDto.latestChangePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                                <image v-if="item.watchesDto.latestChangePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
											</view>
										</view>
										<text class="price-value">¥{{ item.watchesDto.latestUsedPrice || 0 }}</text>
									</view>
								</view>
								<view class="price-box official-price-box">
									<view class="price-content">
										<text class="price-label">公价</text>
										<text class="price-value">¥{{ item.watchesDto.officePrice || 0 }}</text>
									</view>
								</view>
							</view>
							<view class="product-price-row">
								<view class="price-box reference-price-box">
									<view class="price-content">
										<text class="price-label">值达参考价</text>
										<text class="price-value">¥{{ item.watchesDto.minUsedPrice || 0 }} ~ ¥{{
											item.watchesDto.maxUsedPrice || 0 }}</text>
									</view>
								</view>
							</view>
						</view>
						<view class="view-time">{{ item.createTime }}</view>
					</view>

					<!-- 单列视图 -->
					<view class="product-item-single" v-for="(item, index) in historyList" :key="index"
						v-if="!isGridLayout" @click="gotoDetail(item.id)">
						<view class="product-top-row">
							<view class="product-left">
								<image class="product-image" :src="item.watchesDto.imageMain" mode="aspectFit"></image>
							</view>
							<view class="product-info">
								<view class="product-title">
									<text class="product-name">{{ item.watchesDto.brand }} {{ item.watchesDto.series }}</text>
								</view>
								<view class="product-code">
									{{ item.watchesDto.referenceNumber }}
									<view class="copy-btn" @click.stop @click="copyReferenceNumber(item.watchesDto.referenceNumber)"
										style="margin-left: 10px;">
										<image src="https://www.zhida.net/app-resource/images/copy.png" style="width: 12px; height: 12px;"
											mode="aspectFit"></image>
									</view>
								</view>
								<view class="product-rating">
                                    <uni-icons type="star" size="16" color="#999"></uni-icons>
									<text class="rating-score">{{ item.watchesDto.like_num || 0 }}</text>
								</view>
								<view class="view-time-single">{{ item.createTime }}</view>
							</view>
						</view>

						<view class="product-price-container" style="flex-direction: row; gap: 8px;">
							<view class="product-price-row">
								<view class="price-box market-price-box">
									<view class="price-content">
										<view class="price-header">
											<text class="price-label">行情价</text>
											<view class="price-icon-inline">
												<image v-if="item.watchesDto.latestChangePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                                <image v-if="item.watchesDto.latestChangePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
											</view>
										</view>
										<text class="price-value">¥{{ item.watchesDto.latestUsedPrice || '-' }}</text>
									</view>
								</view>
								<view class="price-box official-price-box">
									<view class="price-content">
										<text class="price-label">公价</text>
										<text class="price-value">¥{{ item.watchesDto.officePrice || '-' }}</text>
									</view>
								</view>
							</view>
							<view class="product-price-row">
								<view class="price-box reference-price-box">
									<view class="price-content">
										<text class="price-label">值达参考价</text>
										<text class="price-value">¥{{ item.watchesDto.minUsedPrice || '-' }} ~ ¥{{
											item.watchesDto.maxUsedPrice || '-' }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态展示 -->
				<view v-if="historyList.length === 0 && !isLoading" class="empty-state">
					<uni-icons type="eye" size="50" color="#ddd"></uni-icons>
					<text class="empty-text">暂无浏览记录</text>
				</view>

				<!-- 加载状态 -->
				<view v-if="isLoading" class="loading-more">
					<view class="loading-spinner">
						<view class="spinner"></view>
						<text>加载中...</text>
					</view>
				</view>

				<!-- 没有更多数据提示 -->
				<view v-if="historyList.length > 0 && !hasMore && !isLoading" class="no-more">
					<text>没有更多数据了</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getViewHistory } from '@/utils/api.js'

export default {
	data() {
		return {
			historyList: [],
			isLoading: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 10,
			isGridLayout: true // 默认为多列展示
		}
	},
	onLoad() {
		// 初始化加载浏览记录数据
		this.loadHistoryData();
	},
	// 触底加载更多
	onReachBottom() {
		if (this.hasMore && !this.isLoading) {
			this.loadMore();
		}
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.refreshData().then(() => {
			uni.stopPullDownRefresh();
		}).catch(() => {
			uni.stopPullDownRefresh();
		});
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 切换布局模式（网格/列表）
		toggleLayout() {
			this.isGridLayout = !this.isGridLayout;
		},

		// 刷新数据
		async refreshData() {
			this.pageNum = 1;
			this.historyList = [];
			this.hasMore = true;
			await this.loadHistoryData();
			return Promise.resolve();
		},

		// 加载浏览记录数据
		loadHistoryData() {
			if (this.isLoading) return;

			this.isLoading = true;

			getViewHistory({
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}).then(res => {

				console.log(res);
				const data = res.rows || [];

				if (this.pageNum === 1) {
					this.historyList = data;
				} else {
					this.historyList = [...this.historyList, ...data];
				}

				console.log(this.historyList);


				// 判断是否还有更多数据
				this.hasMore = data.length >= this.pageSize;

				this.isLoading = false;
			}).catch(err => {
				uni.showToast({
					title: err.message || '获取浏览记录失败',
					icon: 'none'
				});

				this.isLoading = false;
			});
		},

		// 加载更多
		loadMore() {
			if (!this.hasMore || this.isLoading) return;

			this.pageNum++;
			this.loadHistoryData();
		},

		// 跳转到商品详情
		gotoDetail(id) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${id}`
			});
		},

		// 跳转到首页
		gotoHome() {
			uni.redirectTo({
				url: '/pages/index/index'
			});
		},

		// 复制编号
        copyReferenceNumber(code) {
            uni.setClipboardData({
                data: code,
                success: function() {
                    uni.showToast({ title: '复制成功', icon: 'success' });
                }
            });
        },
	}
}
</script>

<style>
.history-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

/* 自定义导航栏 */
.custom-nav {
	background-color: #fff;
	position: relative;
	z-index: 99;
	padding-top: var(--status-bar-height);
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 15px;
}

.back-btn {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.page-title {
	font-size: 16px;
	color: #333;
	flex: 1;
	text-align: center;
}

.layout-toggle {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #333;
}

.layout-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 14px;
	height: 14px;
	position: relative;
}

/* Grid icon (多列) */
.grid-icon {
	display: grid;
	grid-template-columns: 1fr 1fr;
	grid-template-rows: 1fr 1fr;
	gap: 1px;
	width: 10px;
	height: 10px;
}

.grid-dot {
	width: 4px;
	height: 4px;
	background-color: #333;
	border-radius: 1px;
}

/* List icon (单列) */
.list-icon {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	width: 10px;
	height: 10px;
}

.list-line {
	height: 2px;
	width: 100%;
	background-color: #333;
	border-radius: 1px;
}

.content-scroll {
	flex: 1;
}

/* 商品列表样式 */
.products-section {
	padding: 10px;
}

.products-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 8px;
}

.products-list {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

/* 多列视图样式 */
.product-item-grid {
	display: flex;
	flex-direction: column;
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
	position: relative;
	padding: 6px;
}

.product-image-wrapper {
	width: 100%;
	height: 84px;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.product-item-grid .product-image {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.product-item-grid .product-info {
	display: flex;
	padding: 6px 0;
	flex-direction: column;
}

.product-item-grid .product-title {
	font-size: 13px;
	line-height: 1.3;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.product-item-grid .product-name {
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
	display: inline-block;
}

.product-item-grid .product-model {
	font-size: 13px;
	color: #666;
	display: flex;
	align-items: center;
}

.product-item-grid .model-number {
	margin-right: 4px;
}

/* 多列视图价格样式 */
.product-item-grid .product-price-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	gap: 4px;
	box-sizing: border-box;
}

.product-item-grid .product-price-row {
	display: flex;
	justify-content: space-between;
	width: 100%;
	box-sizing: border-box;
}

.product-item-grid .price-box {
	flex: 1;
	padding: 6px;
	background-color: #fcfcfc;
	border-radius: 4px;
	position: relative;
	min-width: 0;
	box-sizing: border-box;
	margin: 0;
}

.product-item-grid .price-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	min-width: 0;
	box-sizing: border-box;
}

.product-item-grid .price-header {
	display: flex;
	align-items: center;
	width: 100%;
	margin-bottom: 2px;
	box-sizing: border-box;
	gap: 4px;
}

.product-item-grid .price-label {
	font-size: 10px;
	color: #999;
	text-align: left;
	flex-shrink: 0;
}

.product-item-grid .price-icon-inline {
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

.product-item-grid .price-value {
	font-size: 13px;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: left;
	width: 100%;
}

.product-item-grid .product-price-row:first-child {
	display: flex;
	gap: 8px;
}

.product-item-grid .product-price-row:first-child .price-box {
	flex: 1;
	min-width: 0;
}

.product-item-grid .official-price-box {
	margin-left: 0;
}

/* 单列视图样式 */
.product-item-single {
	display: flex;
	flex-direction: column;
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
	position: relative;
	padding: 6px;
}

.product-top-row {
	display: flex;
}

.product-item-single .product-left {
	width: 80px;
	height: 80px;
	margin-right: 12px;
	background-color: transparent;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.product-item-single .product-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.product-item-single .product-info {
	flex: 1;
	height: auto;
	padding: 0;
	display: flex;
	flex-direction: column;
	background-color: transparent;
}

.product-item-single .product-title {
	font-size: 13px;
	height: auto;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.product-item-single .product-name {
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
	display: inline-block;
}

.product-item-single .product-code {
	font-size: 13px;
	color: #999;
	display: flex;
	align-items: center;
	padding: 4px 0;
}

.copy-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.product-item-single .product-rating {
	position: static;
	display: flex;
	align-items: center;
	background-color: transparent;
	padding: 0;
	margin-bottom: 4px;
	margin-top: 8px;
}

.product-item-single .rating-score {
	font-size: 12px;
	color: #999;
	margin-left: 3px;
}

/* 单列视图价格样式 */
.product-item-single .product-price-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	gap: 4px;
}

.product-item-single .product-price-row {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	gap: 4px;
}

.product-item-single .price-box {
	flex: 1;
	padding: 4px 6px;
	background-color: #f9f9f9;
	border-radius: 4px;
	position: relative;
}

.product-item-single .price-content {
	display: flex;
	flex-direction: column;
	width: 100%;
}

.product-item-single .price-header {
	display: flex;
	align-items: center;
	width: 100%;
	margin-bottom: 2px;
	gap: 4px;
}

.product-item-single .price-label {
	font-size: 10px;
	color: #999;
	text-align: left;
}

.product-item-single .price-icon-inline {
	display: flex;
	align-items: center;
}

.product-item-single .price-value {
	font-size: 13px;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: left;
}

.product-item-single .official-price-box {
	margin-left: 0;
}

/* 收藏按钮样式 */
.product-item-grid .product-rating {
	position: absolute;
	top: 8px;
	right: 8px;
	display: flex;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.8);
	padding: 2px 6px;
	border-radius: 10px;
}

.product-item-grid .rating-score {
	font-size: 12px;
	color: #666;
	margin-left: 3px;
}

/* 布局切换按钮样式 */
.layout-button {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.layout-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 6px;
	width: 14px;
	height: 14px;
	position: relative;
}

/* Grid icon (多列) */
.grid-icon {
	display: grid;
	grid-template-columns: 1fr 1fr;
	grid-template-rows: 1fr 1fr;
	gap: 1px;
	width: 10px;
	height: 10px;
	align-items: center;
	justify-content: center;
}

.grid-dot {
	width: 4px;
	height: 4px;
	background-color: #1a1a1a;
	border-radius: 1px;
}

.active .grid-dot {
	background-color: #1a1a1a;
}

/* List icon (单列) */
.list-icon {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	justify-content: center;
}

.list-line {
	height: 2px;
	width: 100%;
	background-color: #1a1a1a;
	border-radius: 1px;
}

.active .list-line {
	background-color: #1a1a1a;
}

.price-value-container {
	display: flex;
	align-items: center;
	gap: 4px;
}

/* Remove CSS arrow indicators */
.market-price.up::before,
.market-price.down::before {
	display: none;
}

.product-item-single .price-value-with-icon {
	display: flex;
	align-items: center;
	gap: 4px;
	width: 100%;
}

/* Remove CSS arrow indicators */
.product-item-single .market-price-box.up::before,
.product-item-single .market-price-box.down::before {
	display: none;
}

/* 空状态样式 */
.empty-state {
	padding: 50px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-text {
	margin: 20px 0;
	font-size: 16px;
	color: #999;
}

/* 加载状态 */
.loading-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 15px 0;
	color: #666;
	font-size: 14px;
	margin-bottom: 10px;
}

.loading-spinner {
	display: flex;
	align-items: center;
	justify-content: center;
}

.spinner {
	width: 18px;
	height: 18px;
	border: 2px solid #000;
	border-top-color: transparent;
	border-radius: 50%;
	animation: spin 0.8s linear infinite;
	margin-right: 8px;
}

/* 没有更多数据 */
.no-more {
	padding: 15px 0;
	text-align: center;
	color: #999;
	font-size: 14px;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.product-item-grid .product-code {
	font-size: 13px;
	color: #999;
	display: inline-flex;
	align-items: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: calc(100% - 10px);
	gap: 4px;
}

.product-item-grid .product-code .copy-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	margin-left: 4px !important;
}
</style>