<template>
	<view class="security-container">
		
		<view class="security-section">
			<view class="section-title">
				<text>账号绑定</text>
			</view>
			
			<view class="security-list">
				<view class="security-item" @click="handlePhoneEdit">
					<view class="item-left">
						<view class="item-icon phone-icon">
							<uni-icons type="phone-filled" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-name">手机号修改</text>
							<text class="item-value">{{ userInfo.phonenumber }}</text>
						</view>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
				
				<view class="security-item" @click="handleEmailEdit">
					<view class="item-left">
						<view class="item-icon email-icon">
							<uni-icons type="email-filled" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-name">邮箱修改</text>
							<text class="item-value">{{ userInfo.email || '未绑定' }}</text>
						</view>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
				
				<view class="security-item" @click="handlePasswordEdit">
					<view class="item-left">
						<view class="item-icon password-icon">
							<uni-icons type="locked-filled" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-name">密码修改</text>
							<text class="item-value">定期修改密码更安全</text>
						</view>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
			</view>
		</view>
		
		<view class="security-section">
			<view class="section-title">
				<text>账号管理</text>
			</view>
			
			<view class="security-list">
				<view class="security-item" @click="handleLogoutDevices">
					<view class="item-left">
						<view class="item-icon logout-icon">
							<uni-icons type="closeempty" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-name">退出登录</text>
							<!-- <text class="item-value">退出登录后，您将需要重新登录</text> -->
						</view>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
				
				<view class="security-item warning-item" @click="handleDeleteAccount">
					<view class="item-left">
						<view class="item-icon delete-icon">
							<uni-icons type="trash-filled" size="20" color="#fff"></uni-icons>
						</view>
						<view class="item-info">
							<text class="item-name warning-text">注销账号</text>
							<text class="item-value">将永久删除账号及相关信息</text>
						</view>
					</view>
					<uni-icons type="right" size="14" color="#bbb"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				phonenumber: '',
				email: '',
				updateTime: '2023-05-09 14:30:25'
			}
		}
	},
	onLoad() {
		const userInfo = uni.getStorageSync('userInfo');
		this.userInfo = userInfo;
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		// 处理手机号修改
		handlePhoneEdit() {
			uni.navigateTo({
				url: '/pages/security/phone'
			});
		},
		// 处理邮箱修改
		handleEmailEdit() {
			uni.navigateTo({
				url: '/pages/profile/edit'
			});
		},
		// 处理密码修改
		handlePasswordEdit() {
			uni.navigateTo({
				url: '/pages/security/password'
			});
		},
		// 处理退出所有设备
		handleLogoutDevices() {
			uni.showModal({
				title: '提示',
				content: '此操作将会退出您在所有设备上的登录状态，需要重新登录，是否继续？',
				success: (res) => {
					if (res.confirm) {
						// 实际开发中，这里应该调用API退出所有设备
						
						uni.showLoading({
							title: '处理中...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							
							uni.showToast({
								title: '已退出所有设备',
								icon: 'success'
							});
							
							// 退出到登录页面
							setTimeout(() => {
								// 清除登录状态
								const app = getApp();
								app.globalData.isLogin = false;
								uni.removeStorageSync('token');
								uni.removeStorageSync('userInfo');
								
								uni.redirectTo({
									url: '/pages/profile/profile'
								});
							}, 1500);
						}, 1000);
					}
				}
			});
		},
		// 处理注销账号
		handleDeleteAccount() {
			uni.showModal({
				title: '警告',
				content: '注销账号后，您的所有数据将被永久删除且无法恢复。请确认是否继续？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '账号将在7个工作日内完成注销',
							icon: 'none'
						});
					}
				}
			});
		}
	}
}
</script>

<style>

.security-section {
	margin-bottom: 10px;
}

.section-title {
	padding: 15px 15px 10px;
}

.section-title text {
	font-size: 15px;
	color: #666;
}

.security-list {
	background-color: #fff;
}

.security-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.security-item:last-child {
	border-bottom: none;
}

.item-left {
	display: flex;
	align-items: center;
}

.item-icon {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
}

.phone-icon {
	background-color: #3a559f;
}

.email-icon {
	background-color: #5856d6;
}

.wechat-icon {
	background-color: #4fca80;
	overflow: hidden;
}

.wechat-img {
	width: 100%;
	height: 100%;
}

.password-icon {
	background-color: #ff9500;
}

.pay-icon {
	background-color: #4cd964;
}

.fingerprint-icon {
	background-color: #34aadc;
}

.logout-icon {
	background-color: #8e8e93;
}

.delete-icon {
	background-color: #ff3b30;
}

.item-info {
	display: flex;
	flex-direction: column;
}

.item-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
}

.item-value {
	font-size: 14px;
	color: #999;
}

.warning-text {
	color: #ff3b30;
}

.warning-item .item-value {
	color: #ff3b30;
	opacity: 0.7;
}
</style> 