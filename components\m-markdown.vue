<template>
	<view class="m-markdown">
		<rich-text :nodes="parsedMarkdown"></rich-text>
	</view>
</template>

<script>
export default {
	name: 'm-markdown',
	props: {
		content: {
			type: String,
			default: ''
		}
	},
	computed: {
		parsedMarkdown() {
			if (!this.content) return '';
			
			let html = this.content;
			
			// Escape special characters
			html = html.replace(/>/g, '&gt;');
			html = html.replace(/</g, '&lt;');
			
			// Convert line breaks
			html = html.replace(/\n/g, '<br>');
			
			// Headers - h1, h2, h3
			html = html.replace(/### (.*?)(?:<br>|$)/g, '<h3>$1</h3>');
			html = html.replace(/## (.*?)(?:<br>|$)/g, '<h2>$1</h2>');
			html = html.replace(/# (.*?)(?:<br>|$)/g, '<h1>$1</h1>');
			
			// Bold
			html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
			html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');
			
			// Inline code
			html = html.replace(/`([^`]+)`/g, '<code style="background-color:#f0f0f0;padding:2px 4px;border-radius:3px;font-family:monospace;">$1</code>');
			
			// Code blocks
			html = html.replace(/```([\s\S]*?)```/g, '<pre style="background-color:#f0f0f0;padding:8px;border-radius:5px;overflow:auto;font-family:monospace;"><code>$1</code></pre>');
			
			// Links
			html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" style="color:#3a559f;text-decoration:underline;">$1</a>');
			
			// Process list items before italic (to avoid conflicts with asterisks)
			let processedHtml = '';
			let inList = false;
			
			// Process line by line
			const lines = html.split('<br>');
			for (let i = 0; i < lines.length; i++) {
				let line = lines[i];
				
				// Check for list items
				const listMatch = line.match(/^\s*\*\s+(.*?)$/);
				if (listMatch) {
					// Start list if not already in one
					if (!inList) {
						processedHtml += '<ul style="padding-left:20px;margin:8px 0;">';
						inList = true;
					}
					// Add list item with content (without the * marker)
					processedHtml += '<li>' + listMatch[1] + '</li>';
				} else {
					// End list if we were in one
					if (inList) {
						processedHtml += '</ul>';
						inList = false;
					}
					// Add the regular line
					processedHtml += line + (i < lines.length - 1 ? '<br>' : '');
				}
			}
			
			// Close any open list
			if (inList) {
				processedHtml += '</ul>';
			}
			
			html = processedHtml;
			
			// Italic (after list processing to avoid conflicts)
			html = html.replace(/\*([^\*<>]+)\*/g, '<em>$1</em>');
			html = html.replace(/_([^_<>]+)_/g, '<em>$1</em>');
			
			// Horizontal rule
			html = html.replace(/---/g, '<hr style="border:none;border-top:1px solid #eee;margin:10px 0;">');
			
			// Blockquote
			html = html.replace(/&gt; (.*?)(?:<br>|$)/g, '<blockquote style="border-left:4px solid #eee;padding-left:10px;margin-left:5px;color:#666;">$1</blockquote>');
			
			return html;
		}
	}
}
</script>

<style>
.m-markdown {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}
.m-markdown rich-text {
	width: 100%;
	flex: 1;
}
</style> 