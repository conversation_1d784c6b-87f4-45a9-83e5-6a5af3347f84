// API基础路径，根据实际情况修改
// const BASE_URL = 'http://192.168.20.230:8080';
const BASE_URL = 'https://appapi.zhida.net';

/**
 * 登录接口
 * @param {Object} data 登录数据
 * @returns {Promise} Promise对象
 */
export const login = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/loginWithPassword`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 快捷登录接口
 * @param {Object} data 登录数据
 * @returns {Promise} Promise对象
 */
export const quickLogin = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/quickLogin`,
			method: 'POST',
			data,
			success: (res) => {				
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 通过token获取用户信息
 * @param {String} token 登录token
 * @returns {Promise} Promise对象
 */
export const getUserInfoByToken = (token) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/getAppInfo`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 第三方登录接口
 * @param {String} type 登录类型：weixin, qq, apple
 * @returns {Promise} Promise对象
 */
export const socialLogin = (type) => {
	return new Promise((resolve, reject) => {
		// 这里根据不同平台调用不同的登录方法
		switch (type) {
			case 'weixin':
				// 微信登录逻辑
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						// 获取用户信息
						uni.getUserInfo({
							provider: 'weixin',
							success: (infoRes) => {
								// 发送到后端验证
								uni.requestFetch({
									url: `${BASE_URL}/api/login/weixin`,
									method: 'POST',
									data: {
										code: loginRes.code,
										userInfo: infoRes.userInfo
									},
									success: (res) => {
										if (res.statusCode === 200 && res.data.code === 0) {
											uni.setStorageSync('token', res.data.token);
											uni.setStorageSync('userInfo', res.data.userInfo || {});
											resolve(res.data);
										} else {
											reject(res.data || { message: '登录失败' });
										}
									},
									fail: reject
								});
							},
							fail: reject
						});
					},
					fail: reject
				});
				break;

			// 其他平台登录方式类似实现
			default:
				reject({ message: `暂不支持${type}登录` });
		}
	});
};

/**
 * 获取短信验证码
 * @param {String} phone 手机号码
 * @returns {Promise} Promise对象
 */
export const getSmsCode = (phone) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/sms/send`,
			method: 'POST',
			data: { phone },
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 注册接口
 * @param {Object} data 注册数据
 * @returns {Promise} Promise对象
 */
export const register = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/app/register`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
}; 


/**
 * 获取热门品牌/热销品牌
 * @returns {Promise} Promise对象
 */
export const getHotBrandList = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/brands/hot`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '请求失败' });
			}
		});
	});
};

/**
 * 获取品牌列表
 * @param {Object} data 注册数据
 * @returns {Promise} Promise对象
 */
export const getBrandList = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/brands/letter`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '请求失败' });
			}
		});
	});
};

// 获取全部品牌数据
export const getBrandAll = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/brands/all`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '请求失败' });
			}
		});
	});
};

/**
 * 通过品牌ID获取品牌筛选数据
 * @param {Object} data 注册数据
 * @returns {Promise} Promise对象
 */
export const getBrandFilterData = (brandId) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesSearch/series/${brandId}`,
			method: 'GET',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};


/**
 * 查询手表数量
 * @param {String} brandId 品牌ID
 * @returns {Promise} Promise对象
 */
export const getWatchCount = (brandId) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesSearch/count/${brandId}`,
			method: 'GET',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 通过品牌获取商品列表
 * @param {Object} data 注册数据
 * @returns {Promise} Promise对象
 */
export const getProductListByBrand = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesInfo/brandId`,
			method: 'POST', 
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取商品详情
 * @param {String} id 商品ID
 * @returns {Promise} Promise对象
 */
export const getProductDetail = (id) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesSearch/appWatches?id=${id}`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 使用Canvas调整图片大小
 * @param {String} tempFilePath 临时文件路径
 * @param {Number} maxWidth 最大宽度
 * @param {Number} maxHeight 最大高度
 * @param {Number} quality 质量 0-1
 * @returns {Promise} Promise对象
 */
function resizeImageToBase64(tempFilePath, maxWidth = 300, maxHeight = 300, quality = 0.8) {
	return new Promise((resolve, reject) => {
		// 获取图片信息
		uni.getImageInfo({
			src: tempFilePath,
			success: (imageInfo) => {
				console.log('原始图片信息:', imageInfo);
				
				// 计算新的宽高，保持比例
				let newWidth = imageInfo.width;
				let newHeight = imageInfo.height;
				const ratio = imageInfo.width / imageInfo.height;
				
				if (newWidth > maxWidth) {
					newWidth = maxWidth;
					newHeight = newWidth / ratio;
				}
				
				if (newHeight > maxHeight) {
					newHeight = maxHeight;
					newWidth = newHeight * ratio;
				}
				
				// 创建canvas上下文
				const ctx = uni.createCanvasContext('avatarCanvas');
				
				// 绘制图片到canvas
				ctx.drawImage(tempFilePath, 0, 0, newWidth, newHeight);
				ctx.draw(false, () => {
					// 导出图片
					uni.canvasToTempFilePath({
						canvasId: 'avatarCanvas',
						x: 0,
						y: 0,
						width: newWidth,
						height: newHeight,
						destWidth: newWidth,
						destHeight: newHeight,
						quality: quality,
						success: (res) => {
							console.log('Canvas调整后的图片路径:', res.tempFilePath);
							resolve(res.tempFilePath);
						},
						fail: (err) => {
							console.error('Canvas导出图片失败:', err);
							reject(err);
						}
					});
				});
			},
			fail: (err) => {
				console.error('获取图片信息失败:', err);
				reject(err);
			}
		});
	});
}

/**
 * 上传用户头像(通用上传接口)
 * @param {String} filePath 本地文件路径
 * @returns {Promise} Promise对象
 */
export const uploadFile = (filePath) => {
	return new Promise((resolve, reject) => {
		// console.log('开始上传头像, 路径:', filePath);
		
		// 检查token
		const token = uni.getStorageSync('token');
		
		// 直接上传（因为已经在前面处理过图片尺寸）
		uni.uploadFile({
			url: `${BASE_URL}/file/upload`,
			filePath: filePath,
			name: 'file',
			header: {
				'Authorization': 'Bearer ' + token
			},
			success: (res) => {
				console.log(res);
				resolve(res.data);
			},
			fail: (err) => {
				console.error('上传请求失败:', err);
				reject({ message: '网络请求失败' });
			}
		});
	});
};

/**
 * 保存用户个人资料
 * @param {Object} data 用户资料数据
 * @returns {Promise} Promise对象
 */
export const saveUserProfile = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/addUpdate`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 搜索商品
 * @param {Object} data 搜索参数
 * @returns {Promise} Promise对象
 */
export const searchProducts = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesInfo/brandId`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '搜索失败' });
			}
		});
	});
};

/**
 * 获取商品列表
 * @param {Object} data
 * @returns {Promise} Promise对象
 */
export const getProductList = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesSearch/search`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '获取商品列表失败' });
			}
		});
	});
};

/**
 * 判断商品价格是否准确
 * @param {String} id 商品ID
 * @returns {Promise} Promise对象
 */
export const checkPriceAccuracy = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/watchesEvaluate`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 添加或编辑收货地址
 * @param {Object} data 地址信息：id(编辑时需要), name(收货人), phone(手机号码), province(省), city(市), district(区/县), address(详细地址), isDefault(是否默认)
 * @returns {Promise} Promise对象
 */
export const saveAddress = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/address/edit`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取地址详情
 * @param {Number} id 地址ID
 * @returns {Promise} Promise对象
 */
export const getAddressDetail = (id) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/address/info/${id}`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 删除收货地址
 * @param {Number} id 地址ID
 * @returns {Promise} Promise对象
 */
export const deleteAddress = (id) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/address/delete/${id}`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取收货地址列表
 * @returns {Promise} Promise对象
 */
export const getAddressList = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/address/list`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取浏览记录
 * @param {Object} data 分页参数：pageNum(页码), pageSize(每页数量)
 * @returns {Promise} Promise对象
 */
export const getViewHistory = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/watches/view/history`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取收藏商品列表
 * @param {Object} data 分页参数 {pageNum, pageSize}
 * @returns {Promise} Promise对象
 */
export const getFavoriteWatches = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/watches/like/list`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 切换商品收藏状态
 * @param {String} watchesId 商品ID
 * @returns {Promise} Promise对象
 */
export const toggleFavoriteWatch = (watchesId) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/watches/like/${watchesId}`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 提交用户反馈
 * @param {Object} data 反馈数据：type(问题类型), content(问题描述), imgUrls(图片URL数组), contact(联系方式)
 * @returns {Promise} Promise对象
 */
export const submitFeedback = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/feedback`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '提交反馈失败' });
			}
		});
	});
};

/**
 * 更新手机号码
 * @param {Object} data 手机号码数据：phone(新手机号), code(验证码)
 * @returns {Promise} Promise对象
 */
export const updatePhoneNumber = (data) => {
	console.log(data);
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/appPhoneUpdate`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 更新用户密码
 * @param {Object} data 密码数据：oldPassword(旧密码), newPassword(新密码)
 * @returns {Promise} Promise对象
 */
export const updatePassword = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/appUpdatePwd`,
			method: 'POST',
			header: {
				'content-type': 'application/x-www-form-urlencoded'
			},
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 找回密码接口
 * @param {Object} data 找回密码数据：phone(手机号), smsCode(验证码), password(新密码)
 * @returns {Promise} Promise对象
 */
export const forgotPassword = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/appUser/forgotPassword`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取系统通知消息
 */
export const getSystemMessages = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/msg/messageList`,
			method: 'GET',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 系统通知消息已读
 * @param {Object} data 消息ID
 * @returns {Promise} Promise对象
 */
export const readMessage = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/msg/read/${data.id}`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取未读消息数量
 */
export const getUnreadMessageCount = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/system/msg/unread/count`,
			method: 'GET',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 发送消息接口
 * @param {Object} data 
 * @returns {Promise} Promise对象
 */
export const sendMsg = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/api/chat/send`,
			method: 'POST',
			data,
			timeout: 60000, // 60秒超时
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 获取会话消息列表
 * @returns {Promise} Promise对象
 */
export const getChatMessageList = () => {
	return new Promise((resolve, reject) => {

		uni.requestFetch({
			url: `${BASE_URL}/api/chat/list`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '网络请求失败' });
			}
		});
	});
};

/**
 * 发送消息反馈（点赞或踩）
 * @param {Object} data 包含messageId, type(like或dislike), reason(可选，点踩原因)
 * @returns {Promise} Promise对象
 */
export const sendMessageFeedback = (data) => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/api/chat/feedback`,
			method: 'POST',
			data,
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '反馈提交失败' });
			}
		});
	});
};

/**
 * 检查应用更新
 * @returns {Promise} Promise对象，包含版本信息
 */
export const checkForUpdate = () => {
	return new Promise((resolve, reject) => {
		uni.requestFetch({
			url: `${BASE_URL}/app/version/check`,
			method: 'POST',
			success: (res) => {
				resolve(res.data);
			},
			fail: (err) => {
				reject(err || { message: '检查更新失败' });
			}
		});
	});
};

/**
 * 下载并安装更新
 * @param {String} downloadUrl 更新包下载地址
 * @returns {Promise} Promise对象
 */
export const downloadUpdate = (downloadUrl) => {
	return new Promise((resolve, reject) => {		
		const downloadTask = uni.downloadFile({
			url: downloadUrl,
			success: (res) => {
				console.log(res)
				if (res.statusCode === 200) {
					resolve(res.tempFilePath);
				} else {
					uni.hideLoading();
					reject({ message: '下载更新失败' });
				}
			},
			fail: (err) => {
				console.log(err)
				uni.hideLoading();
				reject(err || { message: '下载更新失败' });
			}
		});
	});
};

// 获取一键登录预登录token
export const getUniverifyToken = () => {
	return request({
		url: '/api/login/univerify/token',
		method: 'GET'
	})
}

// 一键登录
export const univerifyLogin = (data) => {
	return request({
		url: '/api/login/univerify',
		method: 'POST',
		data
	})
}
