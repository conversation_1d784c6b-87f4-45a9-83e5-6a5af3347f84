<template>
	<view class="message-container">

		<view class="custom-nav">
			<view class="nav-header">
				<view class="back-btn" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="page-title">消息</view>
				<view class="layout-toggle">
					<text>{{ isGridLayout ? '单列' : '多列' }}</text>
					<view class="layout-icon">
						<view v-if="isGridLayout" class="list-icon">
							<!-- <view class="list-line"></view>
							<view class="list-line"></view>
							<view class="list-line"></view> -->
							<image src="https://www.zhida.net/app-resource/icon/duolie.png" style="width: 12px; height: 12px;"></image>
						</view>
						<view v-else class="grid-icon">
							<!-- <view class="grid-dot"></view>
							<view class="grid-dot"></view>
							<view class="grid-dot"></view>
							<view class="grid-dot"></view> -->
							<image src="https://www.zhida.net/app-resource/icon/danlie.png" style="width: 12px; height: 12px;"></image>
						</view>
					</view>
				</view>
			</view>
		</view>


		<view class="message-list">
			<view v-if="messages.length > 0">
				<view class="message-item" v-for="(item, index) in messages" :key="index" @click="showDetails(item)">
					<view class="message-header">
						<view class="header-left">
							<!-- 消息图标区域 -->
							<view class="message-icon">
								<!-- <uni-icons :type="getIconType(item.messageType)" size="16" color="#fff"></uni-icons> -->
								<image v-if="item.messageType === 'PRICE_ALERT'" src="https://www.zhida.net/app-resource/icon/activity.png"
									mode="aspectFit" class="icon-image"></image>
								<image v-if="item.messageType === 'SYSTEM_READ'" src="https://www.zhida.net/app-resource/icon/system.png"
									mode="aspectFit" class="icon-image"></image>
								<!-- 未读提示红点 -->
								<view v-if="!item.isRead" class="unread-dot"></view>
							</view>
							<!-- 消息标题 -->
							<text class="message-title">{{ item.title }}</text>
						</view>
						<!-- 消息时间 -->
						<text class="message-time">{{ formatTime(item.createTime) }}</text>
					</view>
					<!-- 消息内容 -->
					<view class="message-body">
						<text class="message-content">{{ item.content }}</text>
					</view>
					<!-- 查看详情区域 -->
					<view class="message-footer">
						<text class="detail-text">查看详情</text>
						<uni-icons type="right" size="12" color="#999"></uni-icons>
					</view>
				</view>
			</view>
			<view v-else-if="!loading" class="empty-message">
				<text class="empty-text">暂无消息</text>
			</view>
			<view v-if="loading" class="loading">
				<text>加载中...</text>
			</view>
		</view>
		
		<!-- 消息详情弹窗 -->
		<view class="message-detail-mask" v-if="showDetailPopup" @click="closePopup">
			<view class="message-detail-popup" @click.stop>
				<view class="detail-popup-header">
					<text class="detail-popup-title">{{ currentMessage.title }}</text>
					<view class="detail-popup-close" @click="closePopup">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="detail-popup-content">
					<view class="detail-popup-time">{{ formatFullTime(currentMessage.createTime) }}</view>
					<view class="detail-popup-text">{{ currentMessage.content }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getSystemMessages, readMessage } from '@/utils/api.js';

export default {
	data() {
		return {
			messages: [],
			loading: false,
			currentMessage: {},
			showDetailPopup: false
		}
	},
	onLoad() {
		this.fetchMessages();
	},
	methods: {
		fetchMessages() {
			this.loading = true;
			
			getSystemMessages()
				.then(res => {
					this.loading = false;
					console.log(res);
					this.messages = res.data;
				})
				.catch(err => {
					this.loading = false;
					uni.showToast({
						title: err.message || '获取消息失败',
						icon: 'none'
					});
				});
		},
		formatTime(timestamp) {
			if (!timestamp) return '';
			
			const date = new Date(timestamp);
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			
			return `${hours}:${minutes}`;
		},
		formatFullTime(timestamp) {
			if (!timestamp) return '';
			
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},
		getIconClass(type) {
			if (type === 'PRICE_ALERT') {
				return 'watch-icon';
			}
			return 'system-icon';
		},
		getIconType(type) {
			if (type === 'PRICE_ALERT') {
				return 'info';
			}
			return 'notification-filled';
		},
		showDetails(item) {
			// 设置当前消息
			this.currentMessage = item;
			
			// 打开弹窗
			this.showDetailPopup = true;
			
			// 标记为已读
			this.readMessage(item);
		},
		closePopup() {
			this.showDetailPopup = false;
			// 清空当前消息以确保下次打开时重新加载
			setTimeout(() => {
				this.currentMessage = {};
			}, 200);
		},
		readMessage(item) {
			// 如果已读，则不需要标记
			if (item.isRead) return;
			
			// 标记为已读
			readMessage(item)
				.then(res => {
					if (res.code === 200) {
						// 成功标记为已读
						const index = this.messages.findIndex(msg => msg.id === item.id);
						if (index !== -1) {
							this.messages[index].isRead = true;
						}
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.showToast({
						title: err.message || '操作失败',
						icon: 'none'
					});
				});
		}
	}
}
</script>

<style>
page {
	background-color: #f6f6f6;
}

/* 自定义导航栏 */
.custom-nav {
	background-color: #fff;
	position: relative;
	z-index: 99;
	padding-top: var(--status-bar-height);
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 15px;
}

.back-btn {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.page-title {
	font-size: 16px;
	color: #333;
	flex: 1;
	text-align: center;
}

.layout-toggle {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #333;
	gap: 4px;
	opacity: 0;
}

.layout-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 14px;
	height: 14px;
	position: relative;
}


/* 容器样式 - 整体占满屏幕 */
.message-container {
	background-color: #f6f6f6;
	min-height: 100vh;
	padding: 8px 0;
}

/* 消息列表容器 */
.message-list {
	margin-top: 10px;
	padding: 0 16px; /* 左右外边距16px */
}

/* 单个消息卡片样式 */
.message-item {
	/* 卡片基本样式 */
	background-color: #fff;
	padding: 12px; /* 上下内边距12px，左右内边距16px */
	margin-bottom: 8px; /* 列表项间垂直间距8px */
}

/* 消息卡片头部样式 */
.message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

/* 头部左侧区域（图标+标题） */
.header-left {
	display: flex;
	align-items: center;
}

/* 消息图标样式 */
.message-icon {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px; /* 图标与标题间距8px */
	position: relative; /* 为红点定位做准备 */
}

/* 图标内的图片样式 */
.icon-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

/* 系统通知图标样式 */
.system-icon {
	background-color: #333;
}

/* 腕表资讯图标样式 */
.watch-icon {
	background-color: #D2B48C;
}

/* 未读提示红点样式 */
.unread-dot {
	position: absolute;
	top: -2px;
	right: -2px;
	width: 8px;
	height: 8px;
	background-color: #ff4444;
	border-radius: 50%;
	border: 1px solid #fff;
	z-index: 1;
}

/* 消息标题样式 */
.message-title {
	font-size: 14px;
	color: #333;
	font-weight: bold;
}

/* 消息时间样式 */
.message-time {
	font-size: 12px;
	color: #999;
}

/* 消息内容文本样式 */
.message-content {
	font-size: 13px;
	color: #666;
	line-height: 1.5; /* 行高1.5 */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 最多显示3行 */
	overflow: hidden;
	text-overflow: ellipsis;
    border-bottom: 1px solid #f2f2f2;
    padding: 10px 0;
    margin-bottom: 10px;
}

/* 消息底部区域 */
.message-footer {
	display: flex;
    justify-content: space-between;
	align-items: center;
	height: 16px;
    
}

/* 详情文本样式 */
.detail-text {
	font-size: 12px;
	color: #999;
	margin-right: 4px; /* 文案与图标间距4px */
}

/* 空消息样式 */
.empty-message {
	padding: 50px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-image {
	width: 120px;
	height: 120px;
	margin-bottom: 20px;
}

.empty-text {
	font-size: 16px;
	color: #999;
}

/* 加载中样式 */
.loading {
	padding: 15px 0;
	text-align: center;
	color: #999;
	font-size: 14px;
}

/* 详情弹窗遮罩层 */
.message-detail-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

/* 详情弹窗容器 */
.message-detail-popup {
	width: 85%;
	max-width: 320px;
	background-color: #fff;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
	animation: popup-in 0.2s ease-out forwards;
}

@keyframes popup-in {
	from {
		opacity: 0;
		transform: scale(0.9);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

/* 弹窗头部 */
.detail-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	border-bottom: 1px solid #f0f0f0;
}

/* 弹窗标题 */
.detail-popup-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

/* 关闭按钮 */
.detail-popup-close {
	width: 28px;
	height: 28px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: transparent;
}

/* 弹窗内容区域 */
.detail-popup-content {
	padding: 16px;
	max-height: 60vh;
	overflow-y: auto;
}

/* 消息时间 */
.detail-popup-time {
	font-size: 13px;
	color: #999;
	margin-bottom: 12px;
}

/* 消息内容 */
.detail-popup-text {
	font-size: 15px;
	color: #333;
	line-height: 1.6;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
	page {
		background-color: #1a1a1a;
	}
	
	.message-container {
		background-color: #1a1a1a;
	}
	
	.message-item {
		background-color: #2c2c2c;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
	}
	
	.message-title {
		color: #f0f0f0;
	}
	
	.message-time {
		color: #999;
	}
	
	.message-content {
		color: #bbb;
	}
	
	.detail-text {
		color: #0a84ff; /* 暗黑模式下的蓝色 */
	}
	
	.empty-text {
		color: #aaa;
	}
	
	.message-detail-popup {
		background-color: #2c2c2c;
	}
	
	.detail-popup-header {
		border-bottom: 1px solid #3a3a3a;
	}
	
	.detail-popup-title {
		color: #f0f0f0;
	}
	
	.detail-popup-close {
		background-color: #3a3a3a;
	}
	
	.detail-popup-text {
		color: #eee;
	}
	
	.unread-dot {
		border: 1px solid #2c2c2c; /* 暗黑模式下的边框颜色 */
	}
}
</style> 