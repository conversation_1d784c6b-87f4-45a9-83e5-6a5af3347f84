/**
 * 路由拦截器
 * 用于在页面跳转前检查登录状态
 */

// 需要登录的页面路径列表
const needLoginPages = [
	'/pages/favorite/favorite',
	'/pages/history/history',
	'/pages/message/list',
	'/pages/product/detail',
	'/pages/profile/edit',
	'/pages/security/security',
	'/pages/notification/settings',
	'/pages/address/list',
	'/pages/privacy/settings',
	'/pages/feedback/feedback',
	'/pages/message/message',
]

// 路由拦截方法
export const routeInterceptor = function (url) {
	return new Promise((resolve, reject) => {
		// 获取当前应用实例
		const app = getApp()

		// 判断页面是否需要登录
		const needLogin = needLoginPages.some(page => url.indexOf(page) !== -1)

		if (needLogin) {
			// 需要登录的页面，判断当前是否已登录
			if (app.globalData.isLogin) {
				resolve() // 已登录，允许跳转
			} else {
				uni.navigateTo({
					url: '/pages/login/login?redirect=' + encodeURIComponent(url)
				})

				reject('未登录') // 拒绝当前的跳转请求
			}
		} else {
			resolve() // 不需要登录的页面，直接允许跳转
		}
	})
}

// 重写路由跳转方法
export const initRouteInterceptor = function () {
	// 保存原始的导航方法
	const originNavigateTo = uni.navigateTo
	const originRedirectTo = uni.redirectTo
	const originSwitchTab = uni.switchTab
	const originReLaunch = uni.reLaunch

	// 重写navigateTo
	uni.navigateTo = function (options) {
		console.log(options);

		routeInterceptor(options.url)
			.then(() => {
				originNavigateTo(options)
			})
			.catch(err => {
				console.log('路由拦截:', err)
				// 执行原options的fail回调
				if (options.fail && typeof options.fail === 'function') {
					options.fail({
						errMsg: 'navigateTo:fail ' + err
					})
				}
			})
	}

	// 重写redirectTo
	uni.redirectTo = function (options) {
		routeInterceptor(options.url)
			.then(() => {
				originRedirectTo(options)
			})
			.catch(err => {
				console.log('路由拦截:', err)
				if (options.fail && typeof options.fail === 'function') {
					options.fail({
						errMsg: 'redirectTo:fail ' + err
					})
				}
			})
	}

	// 重写switchTab
	uni.switchTab = function (options) {
		routeInterceptor(options.url)
			.then(() => {
				originSwitchTab(options)
			})
			.catch(err => {
				console.log('路由拦截:', err)
				if (options.fail && typeof options.fail === 'function') {
					options.fail({
						errMsg: 'switchTab:fail ' + err
					})
				}
			})
	}

	// 重写reLaunch
	uni.reLaunch = function (options) {
		routeInterceptor(options.url)
			.then(() => {
				originReLaunch(options)
			})
			.catch(err => {
				console.log('路由拦截:', err)
				if (options.fail && typeof options.fail === 'function') {
					options.fail({
						errMsg: 'reLaunch:fail ' + err
					})
				}
			})
	}
}

// API请求拦截器
export const initRequestInterceptor = function () {
	// 保存原始请求方法
	const originRequest = uni.request

	// 重写请求方法
	uni.requestFetch = function (options) {
		// 获取token
		const token = uni.getStorageSync('token')

		// 创建完整请求配置
		const requestOptions = {
			...options,
			timeout: 30000,
			header: {
				...options.header,
				// 如果有token，则添加到请求头
				...(token ? { 'Authorization': `Bearer ${token}` } : {})
			},
			// 重写success回调
			success: (res) => {

				// 检查响应状态
				if (res.data.code == 401) {
					// 401未授权，清除token并跳转到登录页
					uni.removeStorageSync('token')
					uni.removeStorageSync('userInfo')

					// 更新全局登录状态
					const app = getApp()
					app.globalData.isLogin = false
					app.globalData.userInfo = null

					// 显示提示
					uni.showToast({
						title: '登录已过期，请重新登录',
						icon: 'none',
						duration: 1500
					})

					// 记录当前页面路径，用于登录后跳回
					// const pages = getCurrentPages()
					// const currentPage = pages[pages.length - 1]
					// let redirect = currentPage.route

					// // 如果当前页面有参数，则拼接参数
					// if (currentPage.options && Object.keys(currentPage.options).length > 0) {
					// 	const query = Object.keys(currentPage.options)
					// 		.map(key => `${key}=${currentPage.options[key]}`)
					// 		.join('&')
					// 	redirect = `${redirect}?${query}`
					// }

					// // 延迟跳转到登录页
					// setTimeout(() => {
					// 	uni.redirectTo({
					// 		url: `/pages/login/login?redirect=${encodeURIComponent('/' + redirect)}`
					// 	})
					// }, 1500)

				} 
				else if(res.data.code == 200) {
					// console.log('请求成功');
					
					if (options.success && typeof options.success === 'function') {
						options.success(res)
					}
				} 
				else {
					uni.showToast({
						title: res.data.msg,
						icon: 'none',
						duration: 1500
					})
				}
			},
			fail: (err) => {
				// 处理请求失败，包括超时
				uni.showToast({
					title: err.errMsg.indexOf('timeout') !== -1 ? '请求超时，请检查网络' : '请求失败，请稍后重试',
					icon: 'none',
					duration: 1500
				})
				
				if (options.fail && typeof options.fail === 'function') {
					options.fail(err)
				}
			}
		}

		// 调用原始请求方法
		return originRequest(requestOptions)
	}
} 