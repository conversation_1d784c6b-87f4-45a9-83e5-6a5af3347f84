<template>
	<view class="notification-container">

		<view class="notification-section">
			<!-- <view class="section-title">
				<text>通知方式</text>
			</view> -->
			
			<view class="notification-list">
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">应用通知</text>
						<text class="item-desc">接收应用内的推送通知</text>
					</view>
					<switch :checked="settings.enablePush" color="#3a559f" @change="toggleSetting('enablePush')" />
				</view>
				
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">短信通知</text>
						<text class="item-desc">接收重要信息的短信通知</text>
					</view>
					<switch :checked="settings.enableSMS" color="#3a559f" @change="toggleSetting('enableSMS')" />
				</view>
				
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">邮件通知</text>
						<text class="item-desc">接收重要信息的邮件通知</text>
					</view>
					<switch :checked="settings.enableEmail" color="#3a559f" @change="toggleSetting('enableEmail')" />
				</view>
				
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">声音提醒</text>
						<text class="item-desc">收到通知时播放声音</text>
					</view>
					<switch :checked="settings.enableSound" color="#3a559f" @change="toggleSetting('enableSound')" />
				</view>
				
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">震动提醒</text>
						<text class="item-desc">收到通知时震动提醒</text>
					</view>
					<switch :checked="settings.enableVibration" color="#3a559f" @change="toggleSetting('enableVibration')" />
				</view>
			</view>
		</view>
		
		<!-- <view class="notification-section">
			<view class="section-title">
				<text>免打扰时段</text>
				<text class="hint-text">{{ settings.enableDoNotDisturb ? '开启' : '关闭' }}</text>
			</view>
			
			<view class="notification-list">
				<view class="notification-item">
					<view class="item-info">
						<text class="item-name">免打扰模式</text>
						<text class="item-desc">在指定时间段内不接收通知</text>
					</view>
					<switch :checked="settings.enableDoNotDisturb" color="#3a559f" @change="toggleSetting('enableDoNotDisturb')" />
				</view>
				
				<view class="notification-item time-range-item" v-if="settings.enableDoNotDisturb">
					<view class="time-range">
						<view class="time-picker-container">
							<text class="time-label">开始时间</text>
							<picker mode="time" :value="settings.doNotDisturbStart" @change="changeStartTime">
								<view class="time-value">{{ settings.doNotDisturbStart }}</view>
							</picker>
						</view>
						<text class="time-separator">至</text>
						<view class="time-picker-container">
							<text class="time-label">结束时间</text>
							<picker mode="time" :value="settings.doNotDisturbEnd" @change="changeEndTime">
								<view class="time-value">{{ settings.doNotDisturbEnd }}</view>
							</picker>
						</view>
					</view>
				</view>
			</view>
		</view> -->
		
		<!-- <button class="save-btn" @click="saveSettings">保存设置</button> -->
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				// 通知方式
				enablePush: true,
				enableSMS: false,
				enableEmail: false,
				enableSound: false,
				enableVibration: false,
				
				// 免打扰
				enableDoNotDisturb: false,
				doNotDisturbStart: '22:00',
				doNotDisturbEnd: '08:00'
			}
		}
	},
	onLoad() {
		// 实际开发中，这里应该从API获取用户通知设置
		// 现在使用临时数据
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		// 切换设置项
		toggleSetting(key) {
			this.settings[key] = !this.settings[key];
		},
		// 修改免打扰开始时间
		changeStartTime(e) {
			this.settings.doNotDisturbStart = e.detail.value;
		},
		// 修改免打扰结束时间
		changeEndTime(e) {
			this.settings.doNotDisturbEnd = e.detail.value;
		},
		// 保存设置
		saveSettings() {
			// 实际开发中，这里应该调用API保存用户通知设置
			
			uni.showLoading({
				title: '保存中...'
			});
			
			setTimeout(() => {
				uni.hideLoading();
				
				uni.showToast({
					title: '设置已保存',
					icon: 'success',
					duration: 2000
				});
				
			}, 1000);
		}
	}
}
</script>

<style>

.notification-section {
	margin-bottom: 15px;
}

.section-title {
	padding: 15px 15px 10px;
	display: flex;
	justify-content: space-between;
}

.section-title text {
	font-size: 15px;
	color: #666;
}

.hint-text {
	color: #3a559f;
}

.notification-list {
	background-color: #fff;
}

.notification-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.notification-item:last-child {
	border-bottom: none;
}

.item-info {
	display: flex;
	flex-direction: column;
}

.item-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
}

.item-desc {
	font-size: 14px;
	color: #999;
}

/* 免打扰时间段 */
.time-range-item {
	flex-direction: column;
	align-items: flex-start;
}

.time-range {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10px;
}

.time-picker-container {
	display: flex;
	flex-direction: column;
}

.time-label {
	font-size: 13px;
	color: #999;
	margin-bottom: 5px;
}

.time-value {
	font-size: 16px;
	color: #3a559f;
	padding: 5px 0;
}

.time-separator {
	font-size: 14px;
	color: #999;
}

/* 保存按钮 */
.save-btn {
	width: calc(100% - 40px);
	height: 46px;
	line-height: 46px;
	margin: 30px 20px;
	font-size: 16px;
	color: #fff;
	background-color: #3a559f;
	border-radius: 8px;
	border: none;
	text-align: center;
}
</style>