<template>
    <view class="product-list-container">
        <!-- 自定义导航栏 -->

        <!-- #ifdef APP-PLUS -->
        <view class="custom-nav">
            <image class="nav-bg-image"
                src="https://zhidaoss.13524.net/new/watches/bdda73b9e5c2c08c3fc5e7a87e793c03.png" mode="aspectFill">
            </image>
            <view class="nav-overlay"></view>
            <view class="nav-header">
                <view class="back-btn" @click="goBack">
                    <uni-icons type="left" size="20" color="#fff"></uni-icons>
                </view>
                <view class="search-icon-wrapper" @click="goToSearch">
                    <uni-icons type="search" size="20" color="#fff"></uni-icons>
                </view>
            </view>
            <view class="brand-info">
                <view class="brand-logo-wrapper">
                    <image class="brand-logo" :src="currentBrand.brandLogo || ''" mode="aspectFit"></image>
                </view>
                <view class="brand-title">
                    <text class="brand-name">{{ currentBrand.name }} {{ currentBrand.brandEnglish }}</text>
                    <view class="brand-stats">
                        <view class="stat-badge">
                            <text class="stat-item">
                                <text class="stat-item-number">{{ currentBrand.price_count || 0 }}</text> 
                                <text class="stat-item-unit">+</text>
                                <text class="stat-item-text">行情数据</text>
                            </text>
                        </view>
                        <view class="stat-badge">
                            <text class="stat-item">
                                <text class="stat-item-number">{{ currentBrand.watch_count || 0 }}</text> 
                                <text class="stat-item-unit">款</text>
                            </text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="brand-description" @click="showBrandDescription">
                <text class="desc-text">{{ currentBrand.description || '暂无品牌描述' }}</text>
                <view class="arrow-right">
                    <uni-icons type="arrowright" size="14" color="#fff"></uni-icons>
                </view>
            </view>
        </view>
        <!-- #endif -->



        <!-- #ifdef MP-WEIXIN -->
        <view class="custom-nav" :style="navBarStyle">
            <image class="nav-bg-image"
                src="https://zhidaoss.13524.net/new/watches/bdda73b9e5c2c08c3fc5e7a87e793c03.png" mode="aspectFill">
            </image>
            <view class="nav-overlay"></view>
            <view class="nav-header" :style="navHeaderStyle">
                <view class="back-btn" @click="goBack">
                    <uni-icons type="left" size="20" color="#fff"></uni-icons>
                </view>
                <!-- #ifdef APP-PLUS -->
                <view class="search-icon-wrapper" @click="goToSearch">
                    <uni-icons type="search" size="20" color="#fff"></uni-icons>
                </view>
                <!-- #endif -->
            </view>
            <view class="brand-info">
                <view class="brand-logo-wrapper">
                    <image class="brand-logo" :src="currentBrand.brandLogo || ''" mode="aspectFit"></image>
                </view>
                <view class="brand-title">
                    <text class="brand-name">{{ currentBrand.name }} {{ currentBrand.brandEnglish }}</text>
                    <view class="brand-stats">
                        <view class="stat-badge">
                            <text class="stat-item">
                                <text class="stat-item-number">{{ currentBrand.price_count || 0 }}</text> 
                                <text class="stat-item-unit">+</text>
                                <text class="stat-item-text">行情数据</text>
                            </text>
                        </view>
                        <view class="stat-badge">
                            <text class="stat-item">
                                <text class="stat-item-number">{{ currentBrand.watch_count || 0 }}</text> 
                                <text class="stat-item-unit">款</text>
                            </text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="brand-description" @click="showBrandDescription">
                <text class="desc-text">{{ currentBrand.description || '暂无品牌描述' }}</text>
                <view class="arrow-right">
                    <uni-icons type="arrowright" size="14" color="#fff"></uni-icons>
                </view>
            </view>
        </view>
        <!-- #endif -->

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 分类选项卡 -->
            <view class="filter-options">
                <scroll-view class="filter-tabs-scroll" scroll-x="true" show-scrollbar="false">
                    <view class="filter-tabs">
                        <view class="filter-tab left-aligned" :class="{ active: activeFilter === 'all' }"
                            @click="toggleFilterType('all')">
                            综合
                        </view>
                        <!-- <view class="filter-tab left-aligned" :class="{ active: activeFilter === 'new' }"
                            @click="toggleFilterType('new')">
                            新品
                        </view> -->
                        <view class="filter-tab price-tab left-aligned" :class="{ active: activeFilter === 'price' }"
                            @click="toggleFilterType('price')">
                            行情价
                            <view class="angle-icons">
                                <view class="angle-icon angle-up"
                                    :class="{ active: orderBy === 'asc' && activeFilter === 'price' }"></view>
                                <view class="angle-icon angle-down"
                                    :class="{ active: orderBy === 'desc' && activeFilter === 'price' }"></view>
                            </view>
                        </view>
                        <view class="filter-tab-spacer"></view>
                        <view class="filter-tab right-aligned" :class="{ active: activeFilter === 'layout' }"
                            @click="toggleLayout">
                            <view class="layout-button">
                                <text :class="{ active: activeFilter === 'layout' }">{{ isGridLayout ? '多列' : '单列'
                                    }}</text>
                                <view class="layout-icon">
                                    <view v-if="isGridLayout" class="grid-icon">
                                        <!-- <view class="grid-dot"></view>
                                        <view class="grid-dot"></view>
                                        <view class="grid-dot"></view>
                                        <view class="grid-dot"></view> -->
                                        <image src="https://www.zhida.net/app-resource/icon/duolie.png" style="width: 12px; height: 12px;"></image>
                                    </view>
                                    <view v-else class="list-icon">
                                        <!-- <view class="list-line"></view>
                                        <view class="list-line"></view>
                                        <view class="list-line"></view> -->
                                        <image src="https://www.zhida.net/app-resource/icon/danlie.png" style="width: 12px; height: 12px;"></image>
                                    </view>
                                </view>

                            </view>
                        </view>
                    </view>
                </scroll-view>

                <!-- 筛选一级选项 -->
                <scroll-view class="filter-subtype-scroll" scroll-x="true" show-scrollbar="false">
                    <view class="filter-subtypes">
                        <view class="filter-subtype" 
                            v-for="(value, key) in filterSubtypes" 
                            :key="key" 
                            :class="{ 
                                active: selectedFilterTypes.includes(key) || currentFilterType === key 
                            }"
                            @click="selectFilterType(key)">
                            <text>{{ value }}</text>
                            <uni-icons type="down" size="12"
                                :color="selectedFilterTypes.includes(key) || currentFilterType === key ? '#d6b391' : '#1a1a1a'"></uni-icons>
                        </view>
                    </view>
                </scroll-view>

                <!-- 筛选展开区域 -->
                <view class="filter-collapse" :class="{ 'filter-collapse-open': showFilterPopup }">
                    <view class="filter-collapse-content">
                        <!-- 根据不同的筛选类型显示不同的选项 -->
                        <view class="filter-category">
                            <text class="filter-category-title" v-for="(item, key) in currentFilterOptions"
                                :key="key" :class="{ active: selectedFilters[currentFilterType] && selectedFilters[currentFilterType].name === item.name }"
                                @click="selectFilterOption(currentFilterType, item)">{{ item.name }}</text>
                        </view>
                        
                        <view class="filter-collapse-footer">
                            <view class="btn-reset" @click="resetFilters(true)">重置</view>
                            <view class="btn-confirm" @click="confirmFilters">确定({{ filterResultCount }}款)</view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 商品列表 -->
            <view class="products-section">
                <view :class="{ 'products-grid': isGridLayout, 'products-list': !isGridLayout }"
                    v-if="products.length > 0">
                    <!-- 多列视图 -->
                    <view class="product-item-grid" v-for="(item, index) in products" :key="index" v-if="isGridLayout"
                        @click="goToDetail(item.id)">
                        <view class="product-image-wrapper">
                            <image class="product-image" :src="item.imageMain" mode="aspectFit"></image>
                            <view class="product-rating">
                                <uni-icons type="star" size="16" color="#999"></uni-icons>
                                <text class="rating-score">{{ item.likeNum || 0 }}</text>
                            </view>
                        </view>
                        <view class="product-info">
                            <view class="product-title">
                                <text class="product-name">{{ item.brand }} {{ item.series }}</text>
                            </view>
                            <view class="product-model">
                                <text class="model-number">{{ item.referenceNumber }}</text>
                                <view class="copy-btn" @click="copyReferenceNumber(item.referenceNumber)" style="margin-left: 10px;">
                                    <image src="https://www.zhida.net/app-resource/icon/1624.png" style="width: 10px; height: 10px;"
                                        mode="aspectFit"></image>
                                </view>
                            </view>
                        </view>
                        <view class="product-price-container">
                            <view class="product-price-row">
                                <view class="price-box market-price-box">
                                    <view class="price-content">
                                        <view class="price-header">
                                            <text class="price-label">行情价</text>
                                            <view class="price-icon-inline">
                                                <image v-if="item.latestChangePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                                <image v-if="item.latestChangePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                            </view>
                                        </view>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.latestUsedPrice || 0 }}</text>
                                    </view>
                                </view>
                                <view class="price-box official-price-box">
                                    <view class="price-content">
                                        <text class="price-label">公价</text>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.officePrice || 0 }}</text>
                                    </view>
                                </view>
                            </view>
                            <view class="product-price-row">
                                <view class="price-box reference-price-box">
                                    <view class="price-content">
                                        <text class="price-label">值达参考价</text>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.minUsedPrice || 0 }} ~ {{
                                            item.maxUsedPrice || 0 }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 单列视图 -->
                    <view class="product-item-single" v-for="(item, index) in products" :key="index"
                        v-if="!isGridLayout" @click="goToDetail(item.id)">
                        <view class="product-top-row">
                            <view class="product-left">
                                <image class="product-image" :src="item.imageMain" mode="aspectFit"></image>
                            </view>
                            <view class="product-info">
                                <view class="product-title">
                                    <text class="product-name">{{ item.brand }} {{ item.series }} {{ item.caseDiameter }} {{ item.dialColor }}</text>
                                </view>
                                <view class="product-code">
                                    {{ item.referenceNumber }}
                                    <view class="copy-btn" @click.stop @click="copyReferenceNumber(item.code)" style="margin-left: 6px;">
                                        <image src="https://www.zhida.net/app-resource/icon/1624.png" style="width: 10px; height: 10px;"
                                            mode="aspectFit"></image>
                                    </view>
                                </view>
                                <view class="product-rating">
                                    <uni-icons type="star" size="16" color="#999"></uni-icons>
                                    <text class="rating-score">{{ item.likeNum || 0 }}</text>
                                </view>
                            </view>
                        </view>

                        <view class="product-price-container">
                            <view class="product-price-row">
                                <view class="price-box market-price-box">
                                    <view class="price-content">
                                        <view class="price-header">
                                            <text class="price-label">行情价</text>
                                            <view class="price-icon-inline">
                                                <image v-if="item.latestChangePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                                <image v-if="item.latestChangePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
                                                    mode="aspectFit"></image>
                                            </view>
                                        </view>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.latestUsedPrice }}</text>
                                    </view>
                                </view>
                                <view class="price-box official-price-box">
                                    <view class="price-content">
                                        <text class="price-label">公价</text>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.officePrice }}</text>
                                    </view>
                                </view>
                                <view class="price-box reference-price-box">
                                    <view class="price-content">
                                        <text class="price-label">值达参考价</text>
                                        <text class="price-value"><text style="font-size: 8px;">¥</text>{{ item.minUsedPrice }} ~ {{
                                            item.maxUsedPrice }}</text>
                                    </view>
                                </view>
                            </view>
                            <!-- <view class="product-price-row">
                                <view class="price-box reference-price-box">
                                    <view class="price-content">
                                        <text class="price-label">值达参考价</text>
                                        <text class="price-value">¥{{ item.minUsedPrice }} ~ ¥{{
                                            item.maxUsedPrice }}</text>
                                    </view>
                                </view>
                            </view> -->
                        </view>
                    </view>
                </view>

                <!-- 加载更多提示 -->
                <view class="loading-more" v-if="products.length > 0">
                    <view v-if="loading" class="loading-spinner">
                        <view class="spinner"></view>
                        <text>加载中...</text>
                    </view>
                    <text v-else-if="!hasMoreData">没有更多数据了</text>
                </view>

                <!-- 无数据提示 -->
                <view class="no-data" v-if="products.length === 0">
                    <text class="no-data-text">{{ noDataText }}</text>
                </view>
            </view>
        </view>

        <!-- 品牌描述弹窗 -->
        <view class="brand-popup" v-if="showDescriptionPopup" @click.stop="hideDescriptionPopup">
            <view class="brand-popup-content" @click.stop>
                <view class="brand-popup-header">
                    <view class="brand-popup-logo">
                        <image class="popup-logo" :src="currentBrand.brandLogo || ''" mode="aspectFit"></image>
                    </view>
                    <view class="brand-popup-close" @click="hideDescriptionPopup">
                        <uni-icons type="closeempty" size="20" color="#333"></uni-icons>
                    </view>
                </view>
                <scroll-view class="brand-popup-scroll" scroll-y="true">
                    <view class="brand-popup-body">
                        <view class="brand-popup-name">
                            <text class="popup-name-cn">{{ currentBrand.name }}</text>
                            <text class="popup-name-en">{{ currentBrand.brandEnglish }}</text>
                        </view>
                        <view class="brand-popup-stats">
                            <view class="popup-stat-item">
                                <text class="popup-stat-value">{{ currentBrand.price_count || 0 }}+</text>
                                <text class="popup-stat-label">行情数据</text>
                            </view>
                            <view class="popup-stat-divider"></view>
                            <view class="popup-stat-item">
                                <text class="popup-stat-value">{{ currentBrand.watch_count || 0 }}</text>
                                <text class="popup-stat-label">款</text>
                            </view>
                        </view>
                        <text class="brand-popup-text">{{ currentBrand.description || '暂无品牌描述' }}</text>
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
import { getBrandAll, getProductList, getBrandFilterData, getWatchCount } from '@/utils/api.js'

export default {
    data() {
        return {
            brandId: 0, // 外部传入的品牌ID
            currentBrand: {}, // 当前选中的品牌对象
            brands: [], // 全部品牌数据

            products: [], // 商品列表
            orderField: 'id', // 当前排序字段：id, official_quote, new
            orderBy: 'asc', // 排序方向：asc, desc
            pageSize: 10, // 每页数量
            currentPage: 1, // 当前页码
            hasMoreData: true, // 是否有更多数据
            loading: false, // 是否加载中
            totalCount: 0, // 总商品数

            showFilterPopup: false, // 是否显示筛选弹出层
            isGridLayout: false, // 默认为单列展示
            activeFilter: 'all', // 当前激活的一级筛选条件
            
            noDataText: '',

            // 已选择的筛选选项
            selectedFilters: {
                brand: '',
                series: '',
                materials: '',
                movementTypes: '',
                genders: '',
                diameterRanges: ''
            },

            filterOptions: {},

            currentFilterType: '', // 当前选中的筛选类型
            selectedFilterTypes: [], // 新增：存储多个选中的筛选类型
            currentFilterOptions: [], // 当前选中的筛选类型值
            filterResultCount: 0, // 筛选结果数量

            // 当前选中参数组
            currentFilterGroup: {},

            showDescriptionPopup: false, // 是否显示品牌描述弹窗
            
            // 筛选二级选项数据
            filterSubtypes: {
                brand: '品牌',
                series: '系列',
                materials: '表壳材质',
                movementTypes: '机芯类型',
                genders: '适用性别',
                diameterRanges: '表径'
            },

            statusBarHeight: 0, // 保存状态栏高度
            menuButtonInfo: {}, // 保存胶囊按钮信息
            navBarStyle: {}, // 保存导航栏样式
            navHeaderStyle: {} // 保存导航栏头部样式
        }
    },
    onLoad(options) {   

        // 从其他页面传递的品牌参数
        if (options.brandId) {
            this.brandId = parseInt(options.brandId) || 0;
        }

        // 获取全部品牌数据
        this.getBrandAll();
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.fetchProducts(true);
    },
    // 滚动到底部加载更多
    onReachBottom() {
        if (this.loading || !this.hasMoreData) return;

        console.log('滚动到底部，加载更多数据');
        this.currentPage++;
        this.fetchProducts();
    },
    // 监听currentBrand变化
    watch: {
        currentBrand: {
            async handler(newVal) {
                if (newVal && newVal.id) {
                    // 获取品牌的手表数量和行情数量
                    try {
                        const countRes = await getWatchCount(newVal.id);
                        this.currentBrand.price_count = countRes.data.price_count || 0;
                        this.currentBrand.watch_count = countRes.data.watch_count || 0;
                    } catch (countError) {
                        console.log('获取品牌数量数据失败:', countError);
                    }
                    
                    // 根据品牌ID获取品牌筛选数据
                    await this.getBrandFilterData(newVal.id);
                }
            },
            deep: true
        }
    },
    methods: {

        // 获取所有品牌信息
        async getBrandAll() {
            try {
                const res = await getBrandAll();
                this.brands = res.data;
                console.log(this.brands);

                // 根据品牌ID获取品牌信息
                this.currentBrand = this.brands.find(item => item.id === this.brandId);

            } catch (error) {
                console.log('获取品牌数据失败:', error);
            }
        },

        // 根据品牌ID获取品牌筛选数据
        async getBrandFilterData(brandId) {
            try {
                const res = await getBrandFilterData(brandId);

                // 重新定义数据结构,全部重新定义成{id: '', name: ''}
                const filterOptions = {};

                // 品牌
                filterOptions.brand = this.brands.map(item => ({
                    id: item.id,
                    paramName: 'brandId',
                    name: item.name + '/' + item.brandEnglish,
                    isSelected: item.id === brandId // 添加isSelected属性，默认选中当前品牌
                }));

                // 系列
                filterOptions.series = res.data.series.map(item => ({
                    id: item.id,
                    paramName: 'seriesId',
                    name: item.seriesName,
                    isSelected: false
                }));
                
                // 表壳材质
                filterOptions.materials = res.data.materials.map(item => ({
                    id: item.id,
                    paramName: 'caseMaterialId',
                    name: item.caseMaterial,
                    isSelected: false
                }));
                
                // 机芯类型
                filterOptions.movementTypes = res.data.movementTypes.map(item => ({
                    id: 0,
                    paramName: 'movementType',
                    name: item,
                    isSelected: false
                }));
                
                // 适用性别
                filterOptions.genders = res.data.genders.map(item => ({
                    id: 0,
                    paramName: 'gender',
                    name: item,
                    isSelected: false
                }));
                
                // 表径
                filterOptions.diameterRanges = res.data.diameterRanges.map(item => ({
                    id: 0,
                    paramName: 'diameterRange',
                    name: item,
                    isSelected: false
                }));
                
                // 更新筛选选项
                this.filterOptions = filterOptions;
                
                // 如果有brandId参数，自动选中对应的品牌
                if (brandId) {
                    const selectedBrand = filterOptions.brand.find(item => item.id === brandId);
                    if (selectedBrand) {
                        // 更新选中的品牌
                        selectedBrand.isSelected = true;
                        // 更新选中的品牌
                        this.selectedFilters['brand'] = selectedBrand;
                        // 更新筛选类型标题
                        this.filterSubtypes.brand = selectedBrand.name;
                        // 更新参数组
                        this.currentFilterGroup['brandId'] = selectedBrand.id;
                        // 更新选中样式
                        this.currentFilterType = "brand";
                        // 初始化选中的筛选类型数组，确保品牌被选中
                        if (!this.selectedFilterTypes.includes('brand')) {
                            this.selectedFilterTypes.push('brand');
                        }
                        // 加载品牌筛选选项
                        this.loadFilterOptions("brand");
                    }
                }

                // 获取商品数据
                this.fetchProducts();

            } catch (error) {
                console.log('获取品牌筛选数据失败:', error);
            }
        },

        // 复制编号
        copyReferenceNumber(code) {
            uni.setClipboardData({
                data: code,
                success: function() {
                    uni.showToast({ title: '复制成功', icon: 'success' });
                }
            });
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 跳转到搜索页面
        goToSearch() {
            uni.navigateTo({
                url: '/pages/search/search'
            });
        },


        // 获取商品数据
        async fetchProducts(isPullDown = false) {
            if (this.loading) return;

            // 显示加载中，仅在首次加载或下拉刷新时显示弹窗，滚动到底部加载更多时不显示
            // if (!isPullDown && this.currentPage === 1) {
            //     uni.showLoading({
            //         title: '加载中'
            //     });
            // }

            this.loading = true;

            try {
                // 构建基础请求参数（分页和排序）
                const params = {
                    pageNum: this.currentPage,
                    pageSize: this.pageSize,
                    orderField: this.orderField,
                    orderBy: this.orderBy
                };

                // 合并currentFilterGroup中的筛选条件
                Object.assign(params, this.currentFilterGroup);
                
                console.log('请求参数:', params);

                // 调用API获取数据
                const res = await getProductList(params);
                console.log(res);
                
                // 更新总数和品牌数据
                this.filterResultCount = res.total;

                // 更新无数据提示
                if (res.rows && res.rows.length === 0) {
                    this.noDataText = '暂无相关商品';
                } else {
                    this.noDataText = '';
                }
                
                // 正确处理分页数据
                if (res.rows && res.rows.length > 0) {
                    if (this.currentPage === 1) {
                        // 第一页直接替换数据
                        this.products = res.rows;
                    } else {
                        // 加载更多时，追加数据
                        this.products = [...this.products, ...res.rows];
                    }
                    
                    // 判断是否还有更多数据
                    this.hasMoreData = res.rows.length === this.pageSize;
                } else {
                    if (this.currentPage === 1) {
                        // 第一页没有数据，清空列表
                        this.products = [];
                    }
                    // 没有更多数据了
                    this.hasMoreData = false;
                }

            } catch (error) {
                uni.showToast({
                    title: '获取商品数据失败',
                    icon: 'none'
                });

                if (this.currentPage === 1) {
                    // this.products = [];
                }
                this.hasMoreData = false;
            } finally {
                this.loading = false;

                if (isPullDown) {
                    uni.stopPullDownRefresh();
                } else if (this.currentPage === 1) {
                    // 只在首次加载时隐藏loading
                    // uni.hideLoading();
                }
            }
        },

        // 切换筛选类型（多选）
        toggleFilterType(type) {
            // 设置当前激活的筛选条件
            this.activeFilter = type;
            
            // 根据筛选类型设置排序字段和方向
            if (type === 'all') {
                // 综合排序，按ID排序
                this.orderField = 'id';
                this.orderBy = 'asc';
            } else if (type === 'price') {
                // 行情价排序，按照市场价排序
                this.orderField = 'usedMarketPrice';
                
                // 切换排序方向
                this.orderBy = this.orderBy === 'asc' ? 'desc' : 'asc';
            }
            
            // 重置分页
            this.currentPage = 1;
            this.hasMoreData = true;
            
            // 重新获取商品数据
            this.fetchProducts();
        },

        // 选择筛选类型
        selectFilterType(type) {
            console.log(type);

            // 如果点击已选中的筛选类型，则切换弹出层状态
            if (this.currentFilterType === type) {
                this.showFilterPopup = !this.showFilterPopup;
            } else {
                // 如果点击新的筛选类型，更新类型并保持弹出层展开
                this.currentFilterType = type;
                
                // 如果该类型还未被选中，则添加到选中数组中
                if (!this.selectedFilterTypes.includes(type)) {
                    this.selectedFilterTypes.push(type);
                }
                
                // 如果有品牌ID，确保品牌始终保持选中状态
                if (this.brandId && !this.selectedFilterTypes.includes('brand')) {
                    this.selectedFilterTypes.push('brand');
                }
                
                this.showFilterPopup = true;

                // 加载筛选选项
                this.loadFilterOptions(type);
            }
        },

        // 加载筛选选项
        loadFilterOptions(type) {
            // 直接从filterOptions中获取对应type的选项
            this.currentFilterOptions = this.filterOptions[type] || [];

            // 打印当前筛选选项
            console.log(this.currentFilterOptions);
        },

        // 选择筛选选项（单选）
        async selectFilterOption(type, option) {
            // 如果已经选中该选项，则取消选择
            if (this.selectedFilters[type] && this.selectedFilters[type].id === option.id) {
                // 清除选中状态
                this.selectedFilters[type] = '';
                // 重置筛选类型标题为默认值
                this.filterSubtypes[type] = this.getDefaultFilterSubtypeText(type);
                // 从参数组中移除该参数
                delete this.currentFilterGroup[option.paramName];
                
                // 如果不是品牌，则从选中的筛选类型数组中移除
                if (type !== 'brand') {
                    const index = this.selectedFilterTypes.indexOf(type);
                    if (index > -1) {
                        this.selectedFilterTypes.splice(index, 1);
                    }
                }
            } else {
                // 单选：设置选中的选项
                this.selectedFilters[type] = option;
                
                // 把filterSubtypes的defaultText修改为选中的值
                this.filterSubtypes[type] = option.name;
    
                // 提交的参数组
                this.currentFilterGroup[option.paramName] = option.id || option.name;
    
                // 如果是选择品牌，更新整个currentBrand对象
                if (type === 'brand') {
                    // 从brands数组中找到完整的品牌信息
                    const selectedBrand = this.brands.find(item => item.id === option.id);
                    if (selectedBrand) {
                        // 更新当前品牌
                        this.currentBrand = selectedBrand;
                        this.brandId = selectedBrand.id;
                        
                        // 获取品牌的手表数量和行情数量
                        try {
                            const countRes = await getWatchCount(selectedBrand.id);
                            console.log(countRes);
                            this.currentBrand.price_count = countRes.data.price_count || 0;
                            this.currentBrand.watch_count = countRes.data.watch_count || 0;
                        } catch (countError) {
                            console.log('获取品牌数量数据失败:', countError);
                        }
                        
                        // 清空其他筛选条件，只保留品牌
                        Object.keys(this.selectedFilters).forEach(key => {
                            if (key !== 'brand') {
                                this.selectedFilters[key] = '';
                                this.filterSubtypes[key] = this.getDefaultFilterSubtypeText(key);
                            }
                        });
                        
                        // 重置筛选参数组，只保留品牌ID
                        const brandId = this.currentFilterGroup.brandId;
                        this.currentFilterGroup = { brandId };
                        
                        // 重置选中的筛选类型数组，只保留品牌
                        this.selectedFilterTypes = ['brand'];
                        
                        // 根据品牌ID获取品牌筛选数据
                        await this.getBrandFilterData(selectedBrand.id);
                        return; // 提前返回，因为getBrandFilterData会重新获取数据
                    }
                }

                // 确保当前类型在选中数组中
                if (!this.selectedFilterTypes.includes(type)) {
                    this.selectedFilterTypes.push(type);
                }
            }

            // 打印提交的参数组
            console.log('筛选条件:', this.currentFilterGroup);

            // 重置分页参数
            this.currentPage = 1;
            this.hasMoreData = true;
            
            // 根据筛选条件获取商品数据
            this.fetchProducts();
        },
        
        // 获取筛选类型的默认显示文本
        getDefaultFilterSubtypeText(type) {
            const defaultTexts = {
                brand: '品牌',
                series: '系列',
                materials: '表壳材质',
                movementTypes: '机芯类型',
                genders: '适用性别',
                diameterRanges: '表径'
            };
            return defaultTexts[type] || type;
        },

        // 重置筛选条件
        resetFilters(resetAll = false) {
            if (resetAll) {
                // 保存当前品牌信息
                const brandId = this.currentFilterGroup.brandId;
                const brandFilter = this.selectedFilters.brand;
                const brandSubtype = this.filterSubtypes.brand;
                
                // 重置所有筛选条件
                Object.keys(this.filterSubtypes).forEach(type => {
                    // 品牌不重置
                    if (type !== 'brand') {
                        this.selectedFilters[type] = '';
                        // 重置一级选项显示文本
                        this.filterSubtypes[type] = this.getDefaultFilterSubtypeText(type);
                    }
                });
                
                // 重置当前选项的选中状态
                this.currentFilterOptions.forEach(item => {
                    item.isSelected = false;
                });
                
                // 重置筛选参数组，但保留品牌ID
                this.currentFilterGroup = {};
                if (brandId) {
                    this.currentFilterGroup.brandId = brandId;
                    this.selectedFilters.brand = brandFilter;
                    this.filterSubtypes.brand = brandSubtype;
                }

                // 重置选中的筛选类型数组，但保留品牌
                this.selectedFilterTypes = brandId ? ['brand'] : [];
                
                // 重新获取商品数据
                this.currentPage = 1;
                this.hasMoreData = true;
                this.fetchProducts();
            } else if (this.currentFilterType) {
                // 如果当前选中的是品牌，不做任何操作
                if (this.currentFilterType === 'brand') {
                    return;
                }
                
                // 只重置当前显示的筛选类型
                this.selectedFilters[this.currentFilterType] = '';
                // 重置一级选项显示文本
                this.filterSubtypes[this.currentFilterType] = this.getDefaultFilterSubtypeText(this.currentFilterType);
                // 从参数组中删除相关参数
                if (this.currentFilterOptions && this.currentFilterOptions.length > 0) {
                    const paramName = this.currentFilterOptions[0].paramName;
                    if (paramName) {
                        delete this.currentFilterGroup[paramName];
                    }
                }
                
                // 重置当前选项的选中状态
                this.currentFilterOptions.forEach(item => {
                    item.isSelected = false;
                });

                // 从选中的筛选类型数组中移除当前类型
                const index = this.selectedFilterTypes.indexOf(this.currentFilterType);
                if (index > -1) {
                    this.selectedFilterTypes.splice(index, 1);
                }
                
                // 重新获取商品数据
                this.currentPage = 1;
                this.hasMoreData = true;
                this.fetchProducts();
            }
        },

        // 确认筛选条件
        async confirmFilters() {
            this.showFilterPopup = false;
            this.currentPage = 1;
            this.hasMoreData = true;
            // this.products = []; // Clear products when applying new filters
            
            // 获取商品数据
            this.fetchProducts();
        },

        // 切换布局模式（网格/列表）
        toggleLayout() {
            this.isGridLayout = !this.isGridLayout;
            this.activeFilter = 'layout';
        },

        // 跳转到商品详情
        goToDetail(id) {
            uni.navigateTo({
                url: `/pages/product/detail?id=${id}`
            });
        },

        // 显示品牌描述弹窗
        showBrandDescription() {
            this.showDescriptionPopup = true;
        },

        // 隐藏品牌描述弹窗
        hideDescriptionPopup() {
            this.showDescriptionPopup = false;
        },

        getStatusBarHeight() {
            try {
                // 获取系统信息
                const systemInfo = uni.getSystemInfoSync();
                // 获取胶囊按钮的位置信息
                const menuButton = uni.getMenuButtonBoundingClientRect();
                
                // 保存状态栏高度
                this.statusBarHeight = systemInfo.statusBarHeight;
                
                // 保存胶囊按钮信息
                this.menuButtonInfo = {
                    width: menuButton.width,
                    height: menuButton.height,
                    top: menuButton.top,
                    right: menuButton.right,
                    bottom: menuButton.bottom,
                    left: menuButton.left
                };

                // 设置导航栏样式
                this.navBarStyle = {
                    height: `${this.statusBarHeight + 150}px`,
                    paddingTop: `${this.statusBarHeight}px`
                };

                // 设置导航栏头部样式
                this.navHeaderStyle = {
                    marginTop: `${menuButton.top - systemInfo.statusBarHeight}px`,
                    height: `${menuButton.height}px`
                };
                
                console.log('导航栏配置：', {
                    systemInfo,
                    menuButton,
                    statusBarHeight: this.statusBarHeight,
                    navBarStyle: this.navBarStyle,
                    navHeaderStyle: this.navHeaderStyle
                });
            } catch (error) {
                console.error('获取导航栏信息失败：', error);
            }
        },
    }
}
</script>

<style>
.product-list-container {
    padding: 0;
    background-color: #fafafa;
    min-height: 100vh;
    overflow: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    position: relative;
}

/* APP端导航栏样式 */
/* #ifdef APP-PLUS */
.custom-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    padding-top: var(--status-bar-height);
    height: 150px;
    color: #fff;
    background-color: #fff;
}
/* #endif */

/* 小程序端导航栏样式 */
/* #ifdef MP-WEIXIN */
.custom-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    color: #fff;
    background-color: #fff;
}
/* #endif */

.nav-bg-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(150px + var(--status-bar-height));
    z-index: -2;
    object-fit: cover;
}

.nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(150px + var(--status-bar-height));
    background: rgba(0, 0, 0, 0.55);
    z-index: -1;
}

.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 15px;
}

.back-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-icon-wrapper {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-info {
    display: flex;
    align-items: center;
    padding: 0 15px;
    margin-top: 5px;
}

.brand-logo-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.brand-logo {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    margin-right: 2px;
    background-color: #fff;
}

.brand-title {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.brand-stats {
    display: flex;
    align-items: center;
    font-size: 12px;
    gap: 8px;
}

.stat-badge {
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0.8;
    border-radius: 15px;
    padding: 2px 10px;
    border: 1px solid #8c7d6c;
}

.stat-item-number{
    color: #d6b391;
}

.stat-item-text, .stat-item-unit {
    color: #fff;
    margin-left: 5px;
}

.stat-item {
    color: #fff;
    font-size: 12px;
}

.brand-description {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
}

.desc-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.arrow-right {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 内容容器 */
.content-container {
    padding-top: 0;
    position: relative;
    z-index: 90;
}

/* 筛选选项卡 */
.filter-options {
    position: fixed;
    top: calc(150px + var(--status-bar-height));
    left: 0;
    width: 100%;
    z-index: 95;
    background-color: #fafafa;
}

.filter-tabs-scroll {
    width: 100%;
    margin: 6px 0;
}

.filter-tabs {
    display: flex;
    height: 32px;
    width: 100%;
}

.filter-tab {
    padding: 0 10px;
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #1a1a1a;
    position: relative;
}

.filter-tab.left-aligned {
    justify-content: flex-start;
}

.filter-tab.right-aligned {
    justify-content: flex-end;
}

.filter-tab-spacer {
    flex: 1;
}

.filter-tab.active {
    color: #1a1a1a;
    font-weight: bold;
}

.price-tab {
    display: flex;
    align-items: center;
    gap: 4px;
}

.angle-icons {
    display: flex;
    flex-direction: column;
    height: 8px;
    margin-left: 4px;
    position: relative;
    width: 7px;
}

.angle-icon {
    width: 5px;
    height: 5px;
    border-style: solid;
    border-width: 0 1.2px 1.2px 0;
    padding: 0;
    display: inline-block;
    position: absolute;
}

.angle-up {
    transform: rotate(-135deg) skew(-5deg, -5deg);
    top: -0.5px;
    border-color: #666;
}

.angle-down {
    transform: rotate(45deg) skew(-5deg, -5deg);
    bottom: -0.5px;
    border-color: #666;
}

.angle-up.active,
.angle-down.active {
    border-color: #1a1a1a;
}

/* 筛选二级选项 */
.filter-subtype-scroll {
    width: 100%;
    position: relative;
    z-index: 5;
    padding: 0 10px;
}

.filter-subtypes {
    display: flex;
    width: max-content;
}

.filter-subtype {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    font-size: 13px;
    color: #1a1a1a;
    border-radius: 15px;
    margin-right: 8px;
    height: 24px;
    border: 1px solid transparent;
    max-width: 150px;
}

.filter-subtype text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.filter-subtype.active {
    color: #d6b391;
    background-color: rgba(214, 179, 145, 0.1);
    border-color: #d6b391;
}

/* 筛选展开区域 */
.filter-collapse {
    width: 100%;
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
    position: relative;
    z-index: 4;
    background-color: #fff;
}

.filter-collapse-open {
    max-height: 500px;
    /* Large enough to accommodate content */
    /* border-top: 1px solid #f0f0f0; */
}

.filter-collapse-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.filter-category {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.filter-category-title {
    font-size: 12px;
    color: #1a1a1a;
    padding: 6px 10px;
    border-radius: 4px;
    width: calc(33.333% - 7px); /* Calculate 1/3 of width minus gap */
    box-sizing: border-box;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
}

.filter-category-title.active {
    color: #d6b391;
}

.filter-collapse-footer {
    display: flex;
    height: 50px;
    position: relative;
    padding: 0 10px;
    align-items: center;
    justify-content: center;
}

.btn-reset,
.btn-confirm {
    flex: 1;
    height: 38px;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    font-weight: bold !important;
}

.btn-reset {
    background-color: #fff;
    color: #333;
    font-weight: normal;
    border-radius: 4px 0 0 4px;
    border-right: none;
    border: 1px solid #1a1a1a;
}

.btn-confirm {
    background-color: #1a1a1a;
    color: #fff;
    border: none;
    font-weight: normal;
    border-radius: 0 4px 4px 0;
    border: 1px solid #1a1a1a;
}

/* 商品列表样式 */
.products-section {
    padding: 10px;
    margin-top: calc(150px + var(--status-bar-height) + 75px);
    position: relative;
    z-index: 1;
}

.products-grid, .products-list {
    position: relative;
    z-index: 1;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.product-item-grid, .product-item-single {
    position: relative;
    z-index: 1;
    background-color: #fff;
}

/* 多列视图样式 */
.product-item-grid {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    padding: 6px;
}

.product-image-wrapper {
    width: 100%;
    height: 84px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-item-grid .product-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.product-item-grid .product-rating {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    /* background-color: rgba(255, 255, 255, 0.8); */
    padding: 2px 6px;
    border-radius: 10px;
}

.product-item-grid .rating-score {
    font-size: 12px;
    color: #666;
    margin-left: 3px;
}

.product-item-grid .product-info {
    display: flex;
    padding: 6px 0;
    flex-direction: column;
}

.product-item-grid .product-title {
    font-size: 13px;
    line-height: 1.3;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-item-grid .product-name {
    color: #333;
    white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
	display: inline-block;
}

.product-item-grid .product-model {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
}

.product-item-grid .model-number {
    margin-right: 4px;
}

/* 多列视图价格样式 */
.product-item-grid .product-price-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 4px;
    box-sizing: border-box;
}

.product-item-grid .product-price-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
}

.product-item-grid .price-box {
    flex: 1;
    padding: 6px;
    background-color: #fcfcfc;
    border-radius: 4px;
    position: relative;
    min-width: 0;
    box-sizing: border-box;
    margin: 0;
}

.product-item-grid .price-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
}

.product-item-grid .price-header {
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
    width: 100%;
    margin-bottom: 2px;
    box-sizing: border-box;
    gap: 4px;
}

.product-item-grid .price-label {
    font-size: 10px;
    color: #999;
    text-align: left;
    flex-shrink: 0;
}

.product-item-grid .price-icon-inline {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.product-item-grid .price-value {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    width: 100%;
}

.product-item-grid .product-price-row:first-child {
    display: flex;
    gap: 8px;
}

.product-item-grid .product-price-row:first-child .price-box {
    flex: 1;
    min-width: 0;
}

.product-item-grid .official-price-box {
    margin-left: 0;
}

/* 单列视图样式 */
.product-item-single {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    padding: 6px;
}

.product-top-row {
    display: flex;
}

.product-item-single .product-left {
    width: 80px;
    height: 80px;
    margin-right: 12px;
    background-color: transparent;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.product-item-single .product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-item-single .product-info {
    flex: 1;
    height: auto;
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: transparent;
}

.product-item-single .product-title {
    font-size: 13px;
    height: auto;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.product-item-single .product-name {
    color: #333;
    white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
	display: inline-block;
}

.product-item-single .product-code {
    font-size: 13px;
    color: #999;
    display: flex;
    align-items: center;
    padding: 4px 0;
}

.product-item-single .product-rating {
    position: static;
    display: flex;
    align-items: center;
    background-color: transparent;
    padding: 0;
    margin-bottom: 4px;
    margin-top: 8px;
}

.product-item-single .rating-score {
    font-size: 12px;
    color: #999;
    margin-left: 3px;
}

/* 单列视图价格样式 */
.product-item-single .product-price-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 4px;
}

.product-item-single .product-price-row {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 4px;
}

.product-item-single .price-box {
    flex: 1;
    padding: 4px 6px;
    background-color: #f9f9f9;
    border-radius: 4px;
    position: relative;
}

.product-item-single .price-content {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.product-item-single .price-header {
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
    width: 100%;
    margin-bottom: 2px;
    gap: 4px;
}

.product-item-single .price-label {
    font-size: 10px;
    color: #999;
    text-align: left;
}

.product-item-single .price-icon-inline {
    display: flex;
    align-items: center;
}

.product-item-single .price-value {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
}

.product-item-single .official-price-box {
    margin-left: 0;
}

/* 加载更多提示 */
.loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 18px;
    height: 18px;
    border: 2px solid #000;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin-right: 8px;
}

.loading-more text {
    padding: 8px;
    border-radius: 18px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 无数据提示 */
.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.no-data-image {
    width: 120px;
    height: 120px;
    margin-bottom: 15px;
}

.no-data-text {
    font-size: 16px;
    color: #999;
}

.filter-tab text {
    color: #1a1a1a;
}

.filter-tab text.active,
.filter-tab.active {
    color: #1a1a1a;
    font-weight: bold;
}

.angle-up.active,
.angle-down.active {
    border-color: #1a1a1a;
}

.layout-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.layout-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 6px;
    width: 14px;
    height: 14px;
    position: relative;
}

/* Grid icon (多列) */
.grid-icon {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 1px;
    width: 10px;
    height: 10px;
    align-items: center;
    justify-content: center;
}

.grid-dot {
    width: 4px;
    height: 4px;
    background-color: #1a1a1a;
    border-radius: 1px;
}

.active .grid-dot {
    background-color: #1a1a1a;
}

/* List icon (单列) */
.list-icon {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* width: 10px;
    height: 10px; */
    align-items: center;
    justify-content: center;
}

.list-line {
    height: 2px;
    width: 100%;
    background-color: #1a1a1a;
    border-radius: 1px;
}

.active .list-line {
    background-color: #1a1a1a;
}

.price-value-container {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Remove CSS arrow indicators */
.market-price.up::before,
.market-price.down::before {
    display: none;
}

.product-item-single .price-value-with-icon {
    display: flex;
    align-items: center;
    gap: 4px;
    width: 100%;
}

/* Remove CSS arrow indicators */
.product-item-single .market-price-box.up::before,
.product-item-single .market-price-box.down::before {
    display: none;
}

/* 品牌描述弹窗 */
.brand-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(3px);
}

.brand-popup-content {
    width: 85%;
    max-height: 75%;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: popup-slide-up 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.brand-popup-header {
    height: 120px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background: linear-gradient(to bottom, rgba(214, 179, 145, 0.2), rgba(255, 255, 255, 1) 80%);
    padding-bottom: 20px;
}

.brand-popup-logo {
    position: absolute;
    left: 50%;
    top: 20px;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 3px solid #fff;
    z-index: 2;
}

.popup-logo {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    object-fit: cover;
}

.brand-popup-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    transition: background-color 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.brand-popup-close:active {
    background-color: #e0e0e0;
}

.brand-popup-scroll {
    flex: 1;
    max-height: calc(75vh - 120px);
}

.brand-popup-body {
    padding: 0 24px 30px;
}

.brand-popup-name {
    text-align: center;
    margin-bottom: 20px;
}

.popup-name-cn {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin-right: 8px;
}

.popup-name-en {
    font-size: 20px;
    color: #666;
}

.brand-popup-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 12px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.popup-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 30px;
}

.popup-stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 6px;
}

.popup-stat-label {
    font-size: 13px;
    color: #999;
}

.popup-stat-divider {
    width: 1px;
    height: 36px;
    background-color: #e0e0e0;
}

.brand-popup-text {
    font-size: 15px;
    color: #333;
    line-height: 1.8;
    text-align: justify;
    text-indent: 2em;
}

@keyframes popup-slide-up {
    from {
        transform: translateY(50px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>