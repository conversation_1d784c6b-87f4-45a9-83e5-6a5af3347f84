<template>
	<view class="feedback-container">
		<!-- 添加用于处理图片的隐藏Canvas -->
		<canvas canvas-id="avatarCanvas"
			style="position: absolute; left: -1000px; top: -1000px; width: 800px; height: 800px;"></canvas>

		<view class="feedback-form">
			<view class="form-section">
				<view class="section-title">
					<text>问题类型</text>
				</view>
				<view class="type-tags">
					<view 
						class="type-tag" 
						:class="{ active: selectedType === type.value }" 
						v-for="(type, index) in feedbackTypes" 
						:key="index"
						@click="selectType(type.value)"
					>
						<text>{{ type.label }}</text>
					</view>
				</view>
			</view>
			
			<view class="form-section">
				<view class="section-title">
					<text>请将你的意见和建议告诉我们吧</text>
					<text class="word-count">{{ description.length }}/200</text>
				</view>
				<textarea 
					class="description-input" 
					v-model="description" 
					placeholder="请详细描述您的问题或建议" 
					maxlength="200"
					placeholder-style="font-size: 12px;"
				></textarea>
			</view>
			
			<view class="form-section">
				<view class="section-title">
					<text>上传截图 (选填)</text>
					<text class="hint-text">{{ images.length }}/3</text>
				</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(item, index) in images" :key="index">
							<image class="preview-image" :src="item" mode="aspectFill"></image>
							<view class="delete-icon" @click="deleteImage(index)">
								<uni-icons type="close" size="14" color="#fff"></uni-icons>
							</view>
						</view>
						
						<view class="upload-btn" v-if="images.length < 3" @click="chooseImage">
							<uni-icons type="camera" size="24" color="#ccc"></uni-icons>
						</view>
					</view>
					<text class="upload-hint">上传问题截图，帮助我们更好地解决问题</text>
				</view>
			</view>
			
			<view class="form-section">
				<view class="section-title">
					<text>联系方式 (选填)</text>
				</view>
				<input 
					class="contact-input" 
					v-model="contact" 
					placeholder="请留下您的手机号或邮箱，方便我们回复" 
					placeholder-style="font-size: 12px;"
				/>
			</view>
		</view>
		
		<view class="submit-btn" :class="{ disabled: !isValid || isSubmitting }" @click="submitFeedback">
			{{ isSubmitting ? '提交中...' : '提交反馈' }}
		</view>
	</view>
</template>

<script>
import { submitFeedback, uploadFile } from '@/utils/api.js';

// 图片处理函数 - 复用from edit.vue
function resizeImageToBase64(tempFilePath, maxWidth = 300, maxHeight = 300, quality = 0.8) {
	return new Promise((resolve, reject) => {
		// 获取图片信息
		uni.getImageInfo({
			src: tempFilePath,
			success: (imageInfo) => {
				console.log('原始图片信息:', imageInfo);
				
				// 计算新的宽高，保持比例
				let newWidth = imageInfo.width;
				let newHeight = imageInfo.height;
				const ratio = imageInfo.width / imageInfo.height;
				
				if (newWidth > maxWidth) {
					newWidth = maxWidth;
					newHeight = newWidth / ratio;
				}
				
				if (newHeight > maxHeight) {
					newHeight = maxHeight;
					newWidth = newHeight * ratio;
				}
				
				// 创建canvas上下文
				const ctx = uni.createCanvasContext('avatarCanvas');
				
				// 绘制图片到canvas
				ctx.drawImage(tempFilePath, 0, 0, newWidth, newHeight);
				ctx.draw(false, () => {
					// 导出图片
					uni.canvasToTempFilePath({
						canvasId: 'avatarCanvas',
						x: 0,
						y: 0,
						width: newWidth,
						height: newHeight,
						destWidth: newWidth,
						destHeight: newHeight,
						quality: quality,
						success: (res) => {
							console.log('Canvas调整后的图片路径:', res.tempFilePath);
							resolve(res.tempFilePath);
						},
						fail: (err) => {
							console.error('Canvas导出图片失败:', err);
							reject(err);
						}
					});
				});
			},
			fail: (err) => {
				console.error('获取图片信息失败:', err);
				reject(err);
			}
		});
	});
}

export default {
	data() {
		return {
			feedbackTypes: [
				{ label: '功能建议', value: '功能建议' },
				{ label: '体验问题', value: '体验问题' },
				{ label: '性能问题', value: '性能问题' },
				{ label: '产品咨询', value: '产品咨询' },
				{ label: '内容问题', value: '内容问题' },
				{ label: '其他', value: '其他' }
			],
			selectedType: '功能建议',
			description: '',
			images: [], // 本地图片路径
			imageUrls: [], // 存储上传后的图片URL
			contact: '',
			isSubmitting: false,
			isUploading: false // 添加标志以跟踪上传状态
		}
	},
	computed: {
		isValid() {
			return this.selectedType && this.description.trim().length > 0;
		}
	},
	methods: {

		// 选择问题类型
		selectType(type) {
			this.selectedType = type;
		},
		// 选择图片并立即上传
		async chooseImage() {
			if (this.isUploading) return;
			
			uni.chooseImage({
				count: 1, // 一次只选择一张图片
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: async (res) => {
					try {
						this.isUploading = true;
						
						// 显示上传中提示
						uni.showLoading({
							title: '上传图片中...',
							mask: true
						});
						
						// 获取选择的图片
						const tempFilePath = res.tempFilePaths[0];
						
						// 先将本地图片添加到预览列表
						this.images.push(tempFilePath);
						
						// 上传图片并获取URL
						const imageUrl = await this.uploadImage(tempFilePath);
						
						if (imageUrl) {
							// 上传成功，保存URL
							this.imageUrls.push(imageUrl);
							console.log('上传成功，图片URL:', imageUrl);
							console.log('当前图片URL列表:', this.imageUrls);
							
							uni.hideLoading();
							uni.showToast({
								title: '上传成功',
								icon: 'success',
								duration: 1500
							});
						} else {
							// 上传失败，从预览列表中移除
							const index = this.images.indexOf(tempFilePath);
							if (index !== -1) {
								this.images.splice(index, 1);
							}
							
							uni.hideLoading();
							uni.showToast({
								title: '上传失败',
								icon: 'none',
								duration: 2000
							});
						}
					} catch (error) {
						console.error('上传图片过程中出错:', error);
						
						// 发生错误，从预览列表中移除最后添加的图片
						if (this.images.length > 0) {
							this.images.pop();
						}
						
						uni.hideLoading();
						uni.showToast({
							title: error.message || '上传失败',
							icon: 'none',
							duration: 2000
						});
					} finally {
						this.isUploading = false;
					}
				}
			});
		},
		// 删除图片
		deleteImage(index) {
			// 同时删除本地预览和URL
			this.images.splice(index, 1);
			this.imageUrls.splice(index, 1);
		},
		// 上传单张图片
		async uploadImage(tempFilePath) {
			try {
				// 压缩图片
				const resizedPath = await resizeImageToBase64(tempFilePath, 800, 800, 0.7);
				// 上传图片
				const uploadResponse = await uploadFile(resizedPath);
				
				// 尝试解析返回的数据
				let uploadResult;
				if (typeof uploadResponse === 'string') {
					try {
						uploadResult = JSON.parse(uploadResponse);
					} catch (e) {
						console.error('解析上传结果失败:', e);
						return null;
					}
				} else {
					uploadResult = uploadResponse;
				}
				
				console.log('图片上传返回结果:', uploadResult);
				
				// 返回服务器存储的图片URL
				if (uploadResult && uploadResult.imgUrl) {
					return uploadResult.imgUrl;
				} else if (uploadResult && uploadResult.url) {
					return uploadResult.url;
				} else {
					console.error('无法从响应中获取图片URL');
					return null;
				}
			} catch (error) {
				console.error('上传图片失败:', error);
				throw error;
			}
		},
		// 提交反馈
		async submitFeedback() {
			if (!this.isValid || this.isSubmitting || this.isUploading) return;
			
			this.isSubmitting = true;
			
			uni.showLoading({
				title: '提交中...'
			});
			
			try {
				// 由于图片已经逐个上传，这里直接使用已有的imageUrls
				console.log('提交的图片URL列表:', this.imageUrls);
				
				// 提交反馈内容
				const feedbackData = {
					type: this.selectedType,
					content: this.description,
					images: this.imageUrls,
					contact: this.contact
				};
				
				console.log('提交的反馈数据:', feedbackData);
				
				const result = await submitFeedback(feedbackData);
				
				uni.hideLoading();
				
				if (result.code === 200) {
					uni.showModal({
						title: '提交成功',
						content: '感谢您的反馈，我们会尽快处理并回复！',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
				} else {
					throw new Error(result.msg || '提交失败');
				}
			} catch (error) {
				console.error('提交反馈失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: error.message || '提交失败，请重试',
					icon: 'none',
					duration: 2000
				});
			} finally {
				this.isSubmitting = false;
			}
		}
	}
}
</script>

<style>
.feedback-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 40px;
}

.nav-header {
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
	background-color: #fff;
	position: relative;
}

.nav-back {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
}

.nav-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	width: 30px;
}

.feedback-form {
	margin: 10px;
	border-radius: 8px;
	background-color: #fff;
	overflow: hidden;
}

.form-section {
	padding: 15px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.form-section:last-child {
	border-bottom: none;
}

.section-title {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.section-title text {
	font-size: 16px;
	color: #333;
}

.word-count, .hint-text {
	font-size: 14px;
	color: #999;
}

/* 问题类型 */
.type-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.type-tag {
	padding: 0 15px;
	height: 36px;
	border-radius: 18px;
	background-color: #f5f6fa;
	display: flex;
	align-items: center;
	justify-content: center;
}

.type-tag text {
	font-size: 14px;
	color: #666;
}

.type-tag.active {
	background-color: #1a1a1a;
}

.type-tag.active text {
	color: #fff;
}

/* 问题描述 */
.description-input {
	width: 100%;
	height: 150px;
	padding: 10px;
	background-color: #f8f9fc;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1.5;
}

/* 适配不同平台的 placeholder 样式 */
.description-input::placeholder {
	font-size: 13px !important;
}
.description-input::-webkit-input-placeholder {
	font-size: 13px !important;
}
.description-input:-moz-placeholder {
	font-size: 13px !important;
}
.description-input::-moz-placeholder {
	font-size: 13px !important;
}
.description-input:-ms-input-placeholder {
	font-size: 13px !important;
}

/* 图片上传 */
.image-upload {
	display: flex;
	flex-direction: column;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 10px;
}

.image-item {
	width: 80px;
	height: 80px;
	border-radius: 4px;
	overflow: hidden;
	position: relative;
}

.preview-image {
	width: 100%;
	height: 100%;
}

.delete-icon {
	position: absolute;
	top: 5px;
	right: 5px;
	width: 20px;
	height: 20px;
	border-radius: 10px;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 80px;
	height: 80px;
	border-radius: 4px;
	background-color: #f8f9fc;
	border: 1px dashed #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-hint {
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}

/* 联系方式 */
.contact-input {
	width: 100%;
	height: 40px;
	padding: 0 10px;
	background-color: #f8f9fc;
	border-radius: 8px;
	font-size: 16px;
}

/* 适配不同平台的 placeholder 样式 */
.contact-input::placeholder {
	font-size: 12px !important;
}
.contact-input::-webkit-input-placeholder {
	font-size: 12px !important;
}
.contact-input:-moz-placeholder {
	font-size: 12px !important;
}
.contact-input::-moz-placeholder {
	font-size: 12px !important;
}
.contact-input:-ms-input-placeholder {
	font-size: 12px !important;
}

/* 提交按钮 */
.submit-btn {
	width: calc(100% - 40px);
	height: 46px;
	line-height: 46px;
	margin: 30px 20px;
	font-size: 16px;
	color: #fff;
	background-color: #1a1a1a;
	border-radius: 8px;
	text-align: center;
}

.submit-btn.disabled {
	background-color: #ddd;
	color: #999;
	pointer-events: none;
}
</style> 