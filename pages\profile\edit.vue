<template>
	<view class="edit-profile-container">
		<!-- 添加用于处理图片的隐藏Canvas -->
		<canvas canvas-id="avatarCanvas"
			style="position: absolute; left: -1000px; top: -1000px; width: 300px; height: 300px;"></canvas>

		<view class="avatar-section">
			<view class="avatar-wrapper">
				<image class="avatar" v-if="userInfo.avatarPreview" :src="userInfo.avatarPreview" mode="aspectFill"></image>
				<image class="avatar" v-else-if="userInfo.avatar" :src="userInfo.avatar" mode="aspectFill"></image>
				<image class="avatar" v-else src="https://www.zhida.net/app-resource/icon/moren.png" mode="aspectFill"></image>

				<view class="avatar-edit-icon" @click="chooseAvatar">
					<uni-icons type="camera-filled" size="20" color="#fff"></uni-icons>
				</view>
			</view>
			<text class="avatar-hint">点击修改头像</text>
		</view>

		<view class="form-section">
			<view class="form-item">
				<text class="item-label">昵称</text>
				<input class="item-input" type="text" v-model="userInfo.nickName" placeholder="请输入昵称" />
			</view>
			<view class="form-item">
				<text class="item-label">用户名</text>
				<input class="item-input" type="text" v-model="userInfo.userName" placeholder="请输入用户名" />
			</view>
			<view class="form-item gender-item">
				<text class="item-label">性别</text>
				<view class="gender-options">
					<view class="gender-option" :class="{ active: userInfo.sex == 0 }" @click="userInfo.sex = 0">
						<text>男</text>
					</view>
					<view class="gender-option" :class="{ active: userInfo.sex == 1 }" @click="userInfo.sex = 1">
						<text>女</text>
					</view>
				</view>
			</view>
			<!-- <view class="form-item">
				<text class="item-label">生日</text>
				<picker class="date-picker" mode="date" :value="userInfo.birthday" @change="birthdayChange">
					<view class="picker-text">{{ userInfo.birthday || '请选择生日日期' }}</view>
				</picker>
			</view> -->
			<view class="form-item">
				<text class="item-label">邮箱</text>
				<input class="item-input" type="text" v-model="userInfo.email" placeholder="请输入邮箱" />
			</view>
		</view>

		<button class="save-btn" @click="saveProfile">保存</button>
	</view>
</template>

<script>
import { uploadFile, saveUserProfile } from '@/utils/api.js';

// 图片处理函数
function resizeImageToBase64(tempFilePath, maxWidth = 300, maxHeight = 300, quality = 0.8) {
	return new Promise((resolve, reject) => {
		// 获取图片信息
		uni.getImageInfo({
			src: tempFilePath,
			success: (imageInfo) => {
				console.log('原始图片信息:', imageInfo);

				// 计算新的宽高，保持比例
				let newWidth = imageInfo.width;
				let newHeight = imageInfo.height;
				const ratio = imageInfo.width / imageInfo.height;

				if (newWidth > maxWidth) {
					newWidth = maxWidth;
					newHeight = newWidth / ratio;
				}

				if (newHeight > maxHeight) {
					newHeight = maxHeight;
					newWidth = newHeight * ratio;
				}

				// 创建canvas上下文
				const ctx = uni.createCanvasContext('avatarCanvas');

				// 绘制图片到canvas
				ctx.drawImage(tempFilePath, 0, 0, newWidth, newHeight);
				ctx.draw(false, () => {
					// 导出图片
					uni.canvasToTempFilePath({
						canvasId: 'avatarCanvas',
						x: 0,
						y: 0,
						width: newWidth,
						height: newHeight,
						destWidth: newWidth,
						destHeight: newHeight,
						quality: quality,
						success: (res) => {
							console.log('Canvas调整后的图片路径:', res.tempFilePath);
							resolve(res.tempFilePath);
						},
						fail: (err) => {
							console.error('Canvas导出图片失败:', err);
							reject(err);
						}
					});
				});
			},
			fail: (err) => {
				console.error('获取图片信息失败:', err);
				reject(err);
			}
		});
	});
}

export default {
	data() {
		return {
			userInfo: {
				avatar: '',
				avatarPreview: '',
				nickName: '',
				userName: '',
				sex: 0,
				email: ''
			},
			submitting: false
		}
	},
	onLoad() {

	},
	onShow() {
		const app = getApp();
		if (app.globalData.isLogin) {
			this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
		}
	},
	methods: {
		chooseAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'], // 默认压缩
				sourceType: ['album', 'camera'],
				success: (res) => {
					try {
						const tempFilePaths = res.tempFilePaths;

						// 先显示预览图
						this.userInfo.avatarPreview = tempFilePaths[0];

						uni.showLoading({
							title: '处理图片中...',
							mask: true
						});

						// 先使用Canvas调整图片大小
						resizeImageToBase64(tempFilePaths[0], 300, 300, 0.6)
							.then(resizedPath => {
								uni.showLoading({
									title: '上传中...',
									mask: true
								});

								console.log('处理后的图片路径:', resizedPath);

								// 设置上传超时
								const uploadTimeout = setTimeout(() => {
									uni.hideLoading();
									uni.showToast({
										title: '上传超时，请重试',
										icon: 'none'
									});
								}, 30000);

								// 上传处理后的图片
								return uploadFile(resizedPath)
									.then(data => {
										clearTimeout(uploadTimeout);
										uni.hideLoading();

										try {
											let parsedData = data;
											if (typeof data === 'string') {
												parsedData = JSON.parse(data);
											}

											this.userInfo.avatar = parsedData.imgUrl;
											this.userInfo.avatarPreview = '';

											console.log(this.userInfo);

											uni.showToast({
												title: '头像上传成功',
												icon: 'success'
											});

										} catch (parseError) {
											console.error('解析响应错误:', parseError);
											this.userInfo.avatarPreview = '';

											uni.showToast({
												title: parseError.message || '数据处理出错',
												icon: 'none',
												duration: 2000
											});
										}

										return data;
									})
									.catch(uploadErr => {
										clearTimeout(uploadTimeout);
										uni.hideLoading();
										throw uploadErr;
									});
							})
							.catch(err => {
								uni.hideLoading();
								this.userInfo.avatarPreview = '';
								uni.showToast({
									title: err.message || '上传失败',
									icon: 'none',
									duration: 3000
								});
							});
					} catch (error) {
						uni.hideLoading();
						console.error('处理流程出错:', error);

						this.userInfo.avatarPreview = '';

						uni.showToast({
							title: '处理出错，请重试',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.log('选择图片失败:', err);
				}
			});
		},
		birthdayChange(e) {
			this.userInfo.birthday = e.detail.value;
		},
		async saveProfile() {
			// 防止重复提交
			if (this.submitting) {
				return;
			}

			// 表单验证
			if (!this.userInfo.nickName) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				});
				return;
			}

			if (!this.userInfo.userName) {
				uni.showToast({
					title: '请输入用户名',
					icon: 'none'
				});
				return;
			}

			this.submitting = true;
			uni.showLoading({
				title: '保存中...'
			});

			try {
				const data = {
					avatar: this.userInfo.avatar,
					nickName: this.userInfo.nickName,
					userName: this.userInfo.userName,
					sex: this.userInfo.sex,
					email: this.userInfo.email,
				}

				console.log('提交的数据:', data);

				const res = await saveUserProfile(data)
				console.log('保存结果:', res);

				if (res.code === 200) {
					// 更新全局用户信息
					const app = getApp();
					app.globalData.userInfo = this.userInfo;
					uni.setStorageSync('userInfo', this.userInfo);

					setTimeout(() => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					}, 0)

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					throw new Error(res.message || '保存失败');
				}
			} catch (error) {
				console.error('保存失败:', error);
				uni.showToast({
					title: error.message || '保存失败，请重试',
					icon: 'none',
					duration: 2000
				});
			} finally {
				this.submitting = false;
				uni.hideLoading();
			}
		}
	}
}
</script>

<style>
.edit-profile-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 40px;
}

.avatar-section {
	padding: 30px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #fff;
	margin-bottom: 10px;
}

.avatar-wrapper {
	position: relative;
	width: 90px;
	height: 90px;
	border-radius: 45px;
	overflow: hidden;
	margin-bottom: 10px;
}

.avatar-default {
	width: 90px;
	height: 90px;
	text-align: center;
	line-height: 90px;
	border-radius: 50%;
	background-color: #f8f9fc;
	border: 1px solid rgba(58, 85, 159, 0.1);
}

.avatar {
	width: 100%;
	height: 100%;
}

.avatar-edit-icon {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 30px;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-hint {
	font-size: 14px;
	color: #999;
}

.form-section {
	background-color: #fff;
	padding: 0 20px;
}

.form-item {
	display: flex;
	align-items: center;
	height: 56px;
	border-bottom: 1px solid rgba(58, 85, 159, 0.05);
}

.form-item:last-child {
	border-bottom: none;
}

.item-label {
	width: 80px;
	font-size: 16px;
	color: #333;
}

.item-input {
	flex: 1;
	height: 40px;
	font-size: 16px;
}

.gender-options {
	flex: 1;
	display: flex;
	gap: 20px;
}

.gender-option {
	padding: 6px 20px;
	border-radius: 4px;
	background-color: #f5f6fa;
	font-size: 16px;
	color: #666;
}

.gender-option.active {
	background-color: #1a1a1a;
	color: #fff;
}

.date-picker {
	flex: 1;
}

.picker-text {
	font-size: 16px;
	color: #333;
}

.save-btn {
	width: calc(100% - 40px);
	height: 46px;
	line-height: 46px;
	margin: 30px 20px;
	font-size: 16px;
	color: #fff;
	background-color: #1a1a1a;
	border-radius: 8px;
	border: none;
	text-align: center;
}
</style>