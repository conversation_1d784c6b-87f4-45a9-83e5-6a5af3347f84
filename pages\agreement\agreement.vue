<template>
	<view class="container">
		<!-- Header -->
		<view class="header">
			<view class="back-icon" @click="goBack">
				<uni-icons type="back" size="24" color="#3a5199"></uni-icons>
			</view>
			<text class="title-text">{{title}}</text>
		</view>
		
		<!-- Content -->
		<view class="content-wrapper">
			<scroll-view class="content" scroll-y>
				<view class="section">
					<view v-if="type === 'userAgreement'">
						<text class="section-title">用户协议</text>
						<view class="paragraph">
							<text>欢迎您使用我们的服务！本用户协议（"协议"）是您与我们之间关于使用我们的产品和服务的法律协议。使用我们的服务，即表示您同意本协议的所有条款。</text>
						</view>
						
						<text class="sub-title">1. 服务说明</text>
						<view class="paragraph">
							<text>1.1 我们提供的服务内容包括但不限于：信息发布、信息获取、互动交流等功能。</text>
						</view>
						<view class="paragraph">
							<text>1.2 我们保留随时变更、中断或终止部分或全部服务的权利。</text>
						</view>
						
						<text class="sub-title">2. 用户权利与义务</text>
						<view class="paragraph">
							<text>2.1 您保证在使用我们服务时实施的所有行为均遵守国家法律、法规和本协议的规定以及其他相关规定。</text>
						</view>
						<view class="paragraph">
							<text>2.2 您应对账户安全负责，且应对您账户下的所有行为负责。</text>
						</view>
						
						<text class="sub-title">3. 知识产权</text>
						<view class="paragraph">
							<text>3.1 我们提供的服务中所包含的任何文本、图片、图形、音频和/或视频资料均受版权、商标和/或其他财产所有权法律的保护。</text>
						</view>
						
						<text class="sub-title">4. 隐私保护</text>
						<view class="paragraph">
							<text>4.1 我们重视用户的隐私保护，具体隐私政策请参见《隐私政策》。</text>
						</view>
						
						<text class="sub-title">5. 免责声明</text>
						<view class="paragraph">
							<text>5.1 对于因不可抗力或我们不能控制的原因造成的服务中断或其他缺陷，我们不承担任何责任。</text>
						</view>
						
						<text class="sub-title">6. 协议修改</text>
						<view class="paragraph">
							<text>6.1 我们保留随时修改本协议的权利，修改后的协议条款将在发布后生效。</text>
						</view>
					</view>
					
					<view v-if="type === 'privacyPolicy'">
						<text class="section-title">隐私政策</text>
						<view class="paragraph">
							<text>本隐私政策描述了我们如何收集、使用、存储和共享您的个人信息。我们重视您的隐私，并致力于保护您的个人信息安全。</text>
						</view>
						
						<text class="sub-title">1. 信息收集</text>
						<view class="paragraph">
							<text>1.1 为了提供服务，我们可能收集以下类型的信息：</text>
						</view>
						<view class="paragraph">
							<text>- 账户信息：如手机号码、用户名等</text>
						</view>
						<view class="paragraph">
							<text>- 设备信息：如设备型号、操作系统、唯一设备标识符等</text>
						</view>
						<view class="paragraph">
							<text>- 日志信息：如IP地址、浏览器类型、访问日期和时间等</text>
						</view>
						
						<text class="sub-title">2. 信息使用</text>
						<view class="paragraph">
							<text>2.1 我们使用收集的信息用于以下目的：</text>
						</view>
						<view class="paragraph">
							<text>- 提供、维护和改进我们的服务</text>
						</view>
						<view class="paragraph">
							<text>- 处理您的请求和交易</text>
						</view>
						<view class="paragraph">
							<text>- 发送服务通知和更新</text>
						</view>
						
						<text class="sub-title">3. 信息共享</text>
						<view class="paragraph">
							<text>3.1 除以下情况外，我们不会与第三方共享您的个人信息：</text>
						</view>
						<view class="paragraph">
							<text>- 获得您的明确同意</text>
						</view>
						<view class="paragraph">
							<text>- 法律要求或政府部门的强制性要求</text>
						</view>
						
						<text class="sub-title">4. 信息安全</text>
						<view class="paragraph">
							<text>4.1 我们采取合理的安全措施保护您的个人信息不被未经授权访问、使用或泄露。</text>
						</view>
						
						<text class="sub-title">5. 您的权利</text>
						<view class="paragraph">
							<text>5.1 您有权访问、更正、删除您的个人信息，以及撤回您的同意。</text>
						</view>
						
						<text class="sub-title">6. 政策更新</text>
						<view class="paragraph">
							<text>6.1 我们可能会不时更新本隐私政策，更新后的政策将在发布后生效。</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			type: 'userAgreement', // 'userAgreement' or 'privacyPolicy'
			title: '用户协议'
		};
	},
	onLoad(options) {
		// 接收页面传递过来的参数，确定显示用户协议还是隐私政策
		if (options.type) {
			this.type = options.type;
			this.title = options.type === 'userAgreement' ? '用户协议' : '隐私政策';
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style>
.container {
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #ffffff;
	box-sizing: border-box;
}

.header {
	width: 100%;
	display: flex;
	align-items: center;
	position: relative;
	padding: 20rpx 0;
	margin-bottom: 40rpx;
	margin-top: 60rpx; /* Add top margin to avoid status bar */
}

.back-icon {
	position: absolute;
	left: 0;
	z-index: 1;
}

.icon {
	font-family: "iconfont";
}

.title-text {
	width: 100%;
	text-align: center;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.content-wrapper {
	width: 100%;
	max-width: 750rpx;
	flex: 1;
	border-radius: 20rpx;
	background-color: #f9f9f9;
	padding: 30rpx;
	box-sizing: border-box;
}

.content {
	height: 100%;
}

.section {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.sub-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #d6b391;
	margin: 30rpx 0 20rpx;
	display: block;
}

.paragraph {
	margin-bottom: 20rpx;
}

.paragraph text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

@media screen and (max-height: 700px) {
	.content-wrapper {
		padding: 20rpx;
	}
	
	.section-title {
		margin-bottom: 20rpx;
	}
	
	.paragraph {
		margin-bottom: 15rpx;
	}
}
</style> 