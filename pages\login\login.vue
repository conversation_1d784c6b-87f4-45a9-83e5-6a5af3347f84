<template>
	<view class="container">
		<!-- Logo and Welcome -->
		<view class="header-container">
			<view class="logo-container">
				<image class="logo" src="https://www.zhida.net/app-resource/icon/w-logo.png" mode="aspectFit"></image>
			</view>
			<view class="welcome-container">
				<text class="welcome-text">Hi~ 欢迎来到值达!</text>
			</view>
		</view>

		<!-- Login Form -->
		<view class="form-container">
			<!-- Quick Login Section -->
			<block v-if="provider && !showPhoneLogin">
				<view class="quick-login-section">
					<view class="quick-login-info">
						<text class="quick-login-desc">使用本机号码登录</text>
					</view>

					<view class="form-item phone-display" v-if="quickLoginPhone">
						<text class="current-phone">{{ quickLoginPhone }}</text>
					</view>

					<button class="login-btn primary-btn" @click="handleUniQuickLogin">一键登录</button>

					<view class="other-login" @click="toggleLoginMethod">
						<text>密码登录</text>
					</view>
				</view>
			</block>

			<!-- Phone Login Form -->
			<view class="phone-login-section" v-else>
				<view class="section-title section-title-password">
					<text>手机号登录</text>
				</view>

				<!-- Phone Input -->
				<view class="form-item">
					<view class="phone-prefix">+86</view>
					<input class="phone-input" type="number" v-model="formData.phone" placeholder="请输入手机号"
						placeholder-class="placeholder" />
				</view>

				<!-- Password Input -->
				<view class="form-item">
					<view class="phone-prefix">密码</view>
					<input class="password-input" type="password" v-model="formData.password" placeholder="输入密码"
						placeholder-class="placeholder" style="height: 100rpx;" />
				</view>

				<!-- Bottom Links -->
				<view class="bottom-links">
					<text @click="goToRegister">没有账号？<text style="color: #d6b391;">注册</text></text>
					<text @click="goToForgotPassword">忘记密码?</text>
				</view>

				<!-- Login Button -->
				<button class="login-btn" @click="handleLogin">登录</button>

				<!-- #ifdef APP-PLUS -->
				<view class="other-login" @click="toggleLoginMethod">
					<text>本机一键登录</text>
				</view>
				<!-- #endif -->
			</view>

			<!-- Back to Home -->
			<view class="home-btn" @click="goToHome">
				<text>返回首页</text>
			</view>
		</view>
	</view>
</template>

<script>
import { login, quickLogin, getUserInfoByToken } from '@/utils/api.js'

export default {
	data() {
		return {
			showPhoneLogin: false, // 是否显示手机号登录
			formData: {
				phone: '',
				password: ''
			},
			redirectUrl: '',
			provider: null,
			quickLoginPhone: '',
			univerifyToken: ''
		};
	},
	onLoad(option) {

		// 获取来源页面
		if (option.redirect) {
			this.redirectUrl = decodeURIComponent(option.redirect)
		}
		// 获取uni一键登录服务提供商
		// #ifdef APP-PLUS
		uni.getProvider({
			service: 'oauth',
			success: (res) => {
				if (res.provider && res.provider.indexOf('univerify') > -1) {
					this.provider = 'univerify'
					// 预登录，获取手机号
					this.preLogin()
				}
			}
		})
		// #endif
	},
	methods: {
		// 切换登录方式
		toggleLoginMethod() {
			this.showPhoneLogin = !this.showPhoneLogin;
		},

		// 预登录获取手机号
		async preLogin() {
			try {

				uni.preLogin({
					provider: 'univerify',
					success: async (res) => {
						console.log('预登录成功：', res)
						// 预登录成功，可以进行一键登录
					},
					fail: (err) => {
						console.log('预登录失败：', err)
					}
				})
			} catch (error) {
				console.log('获取预登录token失败：', error)
			}
		},

		// 返回首页
		goToHome() {
			uni.redirectTo({
				url: '/pages/index/index'
			})
		},
		// 处理登录
		async handleLogin() {
			// 表单验证
			if (!this.formData.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				})
				return
			}

			if (!this.formData.password) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				})
				return
			}

			uni.showLoading({
				title: '登录中...'
			})

			try {
				// 获取token
				const res = await login(this.formData)
				console.log(res);

				// 登录后置处理
				await this.handleLoginAfter(res)

			} catch (error) {
				uni.showToast({
					title: error.message || '登录失败!',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 登录后置处理
		async handleLoginAfter(res) {
			uni.setStorageSync('token', res.token)

			// 获取用户信息
			const userInfoRes = await getUserInfoByToken(res.token)
			uni.setStorageSync('userInfo', userInfoRes.user)

			// 更新全局登录状态
			const app = getApp()
			app.globalData.isLogin = true
			app.globalData.userInfo = userInfoRes.user

			uni.showToast({
				title: '登录成功',
				icon: 'success'
			})

			// 登录成功后处理跳转
			setTimeout(() => {
				uni.redirectTo({
					url: '/pages/index/index'
				})
			}, 300)
		},

		goToRegister() {
			uni.redirectTo({
				url: '/pages/register/register'
			});
		},

		goToForgotPassword() {
			uni.redirectTo({
				url: '/pages/login/forgot-password'
			})
		},

		// 修改handleUniQuickLogin方法
		async handleUniQuickLogin() {
			if (!this.provider) {
				uni.showToast({
					title: '当前设备不支持一键登录',
					icon: 'none'
				})
				return
			}

			try {
				// 调用一键登录接口
				uni.login({
					provider: 'univerify',
					univerifyStyle: {
						backgroundColor: '#ffffff',
						phoneNum: {
							color: '#333333',
							fontSize: '14px'
						},
						authButton: {
							normalColor: '#1a1a1a',
							highlightColor: '#333333',
							disabledColor: '#666666',
							textColor: '#ffffff',
							title: '本机号码一键登录'
						},
						privacyTerms: {
							prefix: '我已阅读并同意',
							suffix: '并使用本机号码登录',
							termsColor: '#d6b391'
						},
						icon: {
							path: '/static/logo.png',
							width: '80px',
							height: '80px'
						}
					},
					success: async (res) => {
						const { access_token, openid } = res.authResult;
						console.log('登录凭证：', access_token, openid)

						try {
							const callFunctionResult = await uniCloud.callFunction({
								name: 'getPhoneNumber',
								data: {
									access_token,
									openid
								}
							});
							console.log('云函数调用结果：', callFunctionResult);

							const res = await quickLogin({
								phone: callFunctionResult.result.phoneNumber
							})

							await this.handleLoginAfter(res)

							// 关闭登录弹窗
							uni.closeAuthView()

						} catch (error) {
							console.error('云函数调用失败：', error);
						}
					},
					fail: (err) => {
						this.toggleLoginMethod()
					}
				})

			} catch (error) {
				console.error('一键登录失败：', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			}
		}
	}
};
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #343332;
	position: relative;
	padding: 0;
	box-sizing: border-box;
	overflow: hidden;
}

.container::before {
	content: '';
	position: absolute;
	top: -100px;
	left: -60px;
	width: 210px;
	height: 210px;
	background: linear-gradient(165deg, #999 0%, #343332 90%);
	border-radius: 0 0 140px 0;
	z-index: 0;
}

.header-container {
	margin-top: 200rpx;
	margin-bottom: 80rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.logo-container {
	margin-bottom: 5rpx;
	width: 200rpx;
	height: 120rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.logo {
	width: 100%;
	height: 100%;
}

.welcome-container {
	padding: 0;
	height: 50rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-top: 40rpx;
}

.welcome-text {
	font-size: 36rpx;
	color: #ffffff;
	text-align: center;
}

.form-container {
	width: 100%;
	background-color: #ffffff;
	border-top-left-radius: 60rpx;
	border-top-right-radius: 60rpx;
	padding: 40rpx;
	box-sizing: border-box;
	flex: 1;
}

.form-title {
	margin-bottom: 30rpx;
}

.form-title text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.form-item {
	display: flex;
	height: 100rpx;
	border: 1px solid #f2f2f2;
	border-radius: 8rpx;
	align-items: center;
	margin-bottom: 20rpx;
	box-sizing: border-box;
	overflow: hidden;
}

.phone-prefix {
	font-size: 30rpx;
	color: #333333;
	padding: 0 10rpx;
	width: 100rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
}

.phone-input,
.password-input {
	flex: 1;
	height: 100%;
	font-size: 30rpx;
	color: #333333;
	line-height: 100rpx;
	vertical-align: middle;
	border: none;
	outline: none;
}

.phone-input::placeholder,
.password-input::placeholder {
	color: #ccc;
}

.placeholder {
	color: #ccc;
}

.password-input {
	margin-left: 2rpx;
}

.login-btn {
	width: 100%;
	height: 90rpx;
	background-color: #1a1a1a;
	color: #ffffff;
	font-size: 34rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60rpx;
}

.uni-quick-login-btn {
	width: 100%;
	height: 90rpx;
	background-color: #d6b391;
	color: #ffffff;
	font-size: 34rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 30rpx;
}

.bottom-links {
	display: flex;
	justify-content: space-between;
	margin-top: 40rpx;
	padding: 0 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.bottom-links text {
	font-size: 28rpx;
	color: #999999;
}

.home-btn {
	width: 100%;
	text-align: center;
	margin-top: 60rpx;
	padding: 20rpx 0;
}

.home-btn text {
	font-size: 28rpx;
	color: #666666;
}

.login-link,
.forgot-password {
	display: none;
}

@media screen and (max-height: 700px) {
	.header {
		margin-bottom: 30rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.login-btn {
		margin-top: 30rpx;
		margin-bottom: 40rpx;
	}
}

.login-tabs {
	display: flex;
	justify-content: space-around;
	margin-bottom: 40rpx;
	border-bottom: 1px solid #f2f2f2;
}

.tab-item {
	padding: 20rpx 40rpx;
	position: relative;
}

.tab-item text {
	font-size: 32rpx;
	color: #999;
}

.tab-item.active text {
	color: #333;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: -2rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 40rpx;
	height: 4rpx;
	background-color: #d6b391;
	border-radius: 2rpx;
}

.login-form {
	padding-top: 20rpx;
}

.quick-login-info {
	text-align: center;
	margin-bottom: 40rpx;
}

.quick-login-title {
	font-size: 36rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.quick-login-desc {
	font-size: 28rpx;
	color: #999;
}

.phone-display {
	justify-content: center;
	margin: 40rpx 0;
}

.current-phone {
	font-size: 40rpx;
	color: #333;
	font-weight: bold;
}

.quick-login-section {
	margin-bottom: 40rpx;
}

.divider {
	position: relative;
	text-align: center;
	margin: 40rpx 0;
}

.divider::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	width: 40%;
	height: 1px;
	background-color: #f2f2f2;
}

.divider::after {
	content: '';
	position: absolute;
	right: 0;
	top: 50%;
	width: 40%;
	height: 1px;
	background-color: #f2f2f2;
}

.divider-text {
	display: inline-block;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #999;
	background-color: #fff;
	position: relative;
	z-index: 1;
}

.primary-btn {
	background-color: #1a1a1a;
}

.secondary-btn {
	background-color: #d6b391;
}

.phone-login-section {
	margin-top: 20rpx;
}

.other-login {
	text-align: center;
	margin-top: 40rpx;
}

.other-login text {
	font-size: 28rpx;
	color: #666;
}

.section-title {
	display: flex;
	margin-bottom: 40rpx;
	position: relative;
	align-items: center;
}

.section-title-phone {
	justify-content: center;
	width: 100%;
}

.section-title-phone text:last-child {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.section-title-password {
	justify-content: flex-start;
}

.back-icon {
	position: absolute;
	left: 0;
	font-size: 28rpx !important;
	color: #666 !important;
	font-weight: normal !important;
}

.section-title text {
	font-size: 36rpx;
	color: #333;
	font-weight: bold;
}
</style>
