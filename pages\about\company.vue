<template>
	<view class="company-container">
		<view class="company-banner">
			<image class="banner-image" src="https://via.placeholder.com/750x300/3a559f/fff?text=Company" mode="aspectFill"></image>
		</view>
		
		<view class="company-section">
			<view class="section-title">
				<text>公司简介</text>
			</view>
			
			<view class="section-content">
				<text class="company-description">腕表世界科技有限公司成立于2018年，是一家专注于高端手表电商服务的科技企业。公司总部位于深圳市南山区科技园，拥有一支专业的研发、运营和客服团队。</text>
				
				<text class="company-description">我们以"让每一位腕表爱好者找到心仪的时计"为使命，致力于打造最专业、最便捷的腕表交流与交易平台。通过整合全球优质腕表资源，提供权威的产品信息、专业的购买建议以及贴心的售后服务，为用户带来卓越的购物体验。</text>
				
				<text class="company-description">公司目前已与瑞士、德国、日本等国家的数十家知名腕表品牌建立长期战略合作关系，并在国内主要城市设有线下体验中心，形成了线上+线下相结合的全渠道服务模式。</text>
			</view>
		</view>
		
		<view class="company-section">
			<view class="section-title">
				<text>发展历程</text>
			</view>
			
			<view class="timeline">
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2018年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">公司成立</text>
						<text class="timeline-desc">腕表世界科技有限公司在深圳成立，初始团队10人</text>
					</view>
				</view>
				
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2019年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">产品上线</text>
						<text class="timeline-desc">腕表世界App正式上线，首月注册用户突破10万</text>
					</view>
				</view>
				
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2020年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">业务扩展</text>
						<text class="timeline-desc">开设北京、上海两家线下体验中心，开启O2O模式</text>
					</view>
				</view>
				
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2021年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">品牌合作</text>
						<text class="timeline-desc">与全球30+知名腕表品牌建立战略合作关系</text>
					</view>
				</view>
				
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2022年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">服务升级</text>
						<text class="timeline-desc">推出腕表定制、保养、回收等全方位服务</text>
					</view>
				</view>
				
				<view class="timeline-item">
					<view class="timeline-year">
						<text>2023年</text>
					</view>
					<view class="timeline-content">
						<text class="timeline-title">全面发展</text>
						<text class="timeline-desc">用户总数突破500万，月交易额超1亿元</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="company-section">
			<view class="section-title">
				<text>企业文化</text>
			</view>
			
			<view class="culture-list">
				<view class="culture-item">
					<view class="culture-icon">
						<uni-icons type="star-filled" size="24" color="#3a559f"></uni-icons>
					</view>
					<view class="culture-content">
						<text class="culture-title">愿景</text>
						<text class="culture-desc">成为全球领先的腕表文化与服务平台</text>
					</view>
				</view>
				
				<view class="culture-item">
					<view class="culture-icon">
						<uni-icons type="heart-filled" size="24" color="#3a559f"></uni-icons>
					</view>
					<view class="culture-content">
						<text class="culture-title">使命</text>
						<text class="culture-desc">让每一位腕表爱好者找到心仪的时计</text>
					</view>
				</view>
				
				<view class="culture-item">
					<view class="culture-icon">
						<uni-icons type="medal-filled" size="24" color="#3a559f"></uni-icons>
					</view>
					<view class="culture-content">
						<text class="culture-title">价值观</text>
						<text class="culture-desc">诚信、专业、创新、用户第一</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	methods: {
	}
}
</script>

<style>
.company-container {
	min-height: 100vh;
	background-color: #f8f9fc;
	padding-bottom: 30px;
}

.company-banner {
	width: 100%;
	height: 200px;
	position: relative;
	overflow: hidden;
}

.banner-image {
	width: 100%;
	height: 100%;
}

.company-section {
	margin: 15px 15px 0;
	padding: 15px;
	background-color: #fff;
	border-radius: 8px;
}

.section-title {
	margin-bottom: 15px;
	border-left: 4px solid #3a559f;
	padding-left: 10px;
}

.section-title text {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.company-description {
	font-size: 15px;
	color: #666;
	line-height: 1.6;
	margin-bottom: 15px;
	display: block;
	text-align: justify;
}

.company-description:last-child {
	margin-bottom: 0;
}

/* 发展历程 */
.timeline {
	padding-left: 20px;
	border-left: 1px solid #ddd;
}

.timeline-item {
	position: relative;
	padding-bottom: 20px;
}

.timeline-item:last-child {
	padding-bottom: 0;
}

.timeline-year {
	position: absolute;
	left: -40px;
	width: 60px;
	text-align: right;
}

.timeline-year text {
	font-size: 15px;
	font-weight: 500;
	color: #3a559f;
}

.timeline-content {
	margin-left: 30px;
}

.timeline-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 5px;
	display: block;
}

.timeline-desc {
	font-size: 14px;
	color: #666;
	display: block;
}

.timeline-item:before {
	content: '';
	width: 10px;
	height: 10px;
	background-color: #3a559f;
	border-radius: 50%;
	position: absolute;
	left: -25px;
	top: 5px;
}

/* 企业文化 */
.culture-list {
	margin-top: 10px;
}

.culture-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20px;
}

.culture-item:last-child {
	margin-bottom: 0;
}

.culture-icon {
	margin-right: 15px;
	width: 40px;
	height: 40px;
	background-color: rgba(58, 85, 159, 0.1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.culture-content {
	flex: 1;
}

.culture-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 5px;
	display: block;
}

.culture-desc {
	font-size: 14px;
	color: #666;
	display: block;
}
</style> 