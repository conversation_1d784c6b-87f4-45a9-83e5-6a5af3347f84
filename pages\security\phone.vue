<template>
	<view class="phone-container">
		<view class="form-section">
			<view class="section-title">
				<text>修改手机号</text>
			</view>
			
			<view class="form-group">
				<view class="form-item">
					<view class="input-label">当前手机号</view>
					<view class="current-phone">{{ userInfo.phonenumber }}</view>
				</view>
				
				<view class="form-item">
					<view class="input-label">新手机号</view>
					<input class="input-field" 
						type="number" 
						placeholder="请输入新手机号" 
						maxlength="11" 
						v-model="newPhone"
						@input="validatePhone" />
				</view>
				
				<view class="form-item code-item">
					<view class="input-label">验证码</view>
					<input class="input-field" 
						type="number" 
						placeholder="请输入验证码" 
						maxlength="6" 
						v-model="verifyCode" />
					<view class="code-btn" 
						:class="{ disabled: !isPhoneValid || isCounting }" 
						@click="getVerifyCode">
						{{ codeText }}
					</view>
				</view>
			</view>
			
			<button class="submit-btn" 
				:disabled="!isFormValid" 
				:class="{ disabled: !isFormValid }"
				@click="handleSubmit">
				确认修改
			</button>
		</view>
	</view>
</template>

<script>
import { getSmsCode, updatePhoneNumber } from '../../utils/api.js';

export default {
	data() {
		return {
			userInfo: {
				phonenumber: ''
			},
			newPhone: '',
			verifyCode: '',
			isPhoneValid: false,
			isCounting: false,
			countDown: 60,
			codeText: '获取验证码',
			timer: null
		}
	},
	computed: {
		isFormValid() {
			return this.isPhoneValid && this.verifyCode.length === 6;
		}
	},
	onLoad() {
		// 从缓存获取用户信息
		const userInfo = uni.getStorageSync('userInfo');
		this.userInfo = userInfo;
		console.log(this.userInfo);
		
	},
	onUnload() {
		// 页面卸载时清除定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		// 手机号码脱敏处理
		maskPhone(phone) {
			if (!phone) return '未绑定';
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
		},
		
		// 验证手机号
		validatePhone() {
			const phoneReg = /^1[3-9]\d{9}$/;
			this.isPhoneValid = phoneReg.test(this.newPhone);
			
			// 如果用户输入的新手机号与原手机号相同
			if (this.newPhone === this.userInfo.phone) {
				this.isPhoneValid = false;
				uni.showToast({
					title: '新手机号不能与当前手机号相同',
					icon: 'none'
				});
			}
		},
		
		// 获取验证码
		getVerifyCode() {
			if (!this.isPhoneValid || this.isCounting) return;
			
			// 调用验证码接口
			getSmsCode(this.newPhone).then(res => {
				if (res.code === 200) {
					this.startCountDown();
					uni.showToast({
						title: '验证码已发送',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: res.msg || '验证码发送失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.showToast({
					title: err.message || '验证码发送失败',
					icon: 'none'
				});
			});
		},
		
		// 开始倒计时
		startCountDown() {
			this.isCounting = true;
			this.countDown = 60;
			this.codeText = `${this.countDown}秒`;
			
			this.timer = setInterval(() => {
				this.countDown--;
				this.codeText = `${this.countDown}秒`;
				
				if (this.countDown <= 0) {
					clearInterval(this.timer);
					this.isCounting = false;
					this.codeText = '获取验证码';
				}
			}, 1000);
		},
		
		// 提交修改
		handleSubmit() {
			if (!this.isFormValid) return;
			
			uni.showLoading({
				title: '提交中...'
			});
			
			// 调用修改手机号接口
			updatePhoneNumber({
				phone: this.newPhone,
				smsCode: this.verifyCode
			}).then(res => {
				uni.hideLoading();
				
				if (res.code === 200) {
					uni.showToast({
						title: '修改成功,请重新登录',
						icon: 'success'
					});
					
					// 退出登录
					const app = getApp();
					app.globalData.isLogin = false;
					app.globalData.userInfo = {};

					// 清除用户数据
					uni.removeStorageSync('token');
					uni.removeStorageSync('userInfo');

					// 返回登录页面
					setTimeout(() => {	
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '修改失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.hideLoading();
				
				uni.showToast({
					title: err.message || '网络错误',
					icon: 'none'
				});
			});
		}
	}
}
</script>

<style>
.phone-container {
	padding: 20px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 20px;
	color: #333;
}

.form-section {
	background-color: #fff;
	border-radius: 8px;
	padding: 20px;
}

.form-group {
	margin-bottom: 20px;
}

.form-item {
	margin-bottom: 15px;
}

.input-label {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.current-phone {
	font-size: 16px;
	color: #333;
	padding: 10px 0;
}

.input-field {
	height: 45px;
	border: 1px solid #e0e0e0;
	border-radius: 4px;
	padding: 0 15px;
	font-size: 16px;
	width: 100%;
	box-sizing: border-box;
}

.code-item {
	display: flex;
	flex-direction: column;
	position: relative;
}

.code-btn {
	position: absolute;
	right: 0;
	bottom: 0;
	height: 45px;
	line-height: 45px;
	padding: 0 15px;
	color: #1a1a1a;
	font-size: 14px;
	border-left: 1px solid #e0e0e0;
	background-color: #1a1a1a;
	color: #fff;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.code-btn.disabled {
	color: #999;
	background-color: #f5f5f5;
}

.submit-btn {
	width: 100%;
	height: 45px;
	line-height: 45px;
	background-color: #1a1a1a;
	color: #fff;
	font-size: 16px;
	border-radius: 4px;
	text-align: center;
}

.submit-btn.disabled {
	background-color: #cccccc;
	color: #ffffff;
}
</style> 