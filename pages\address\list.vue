<template>
	<view class="address-container">
		
		<!-- 地址列表 -->
		<view class="address-list" v-if="addressList.length > 0">
			<view class="address-item" v-for="(item, index) in addressList" :key="item.id">
				<view class="address-content" @click="selectAddress(item)">
					<view class="address-info">
						<view class="contact-info">
							<text class="name">{{ item.name }}</text>
							<text class="phone">{{ item.phone }}</text>
							<view class="tag" v-if="item.isDefault">
								<text>默认</text>
							</view>
						</view>
						<text class="address-text">{{ item.province }} {{ item.city }} {{ item.district }} {{ item.address }}</text>
					</view>
				</view>
				<view class="address-actions">
					<view class="action-btn" @click="editAddress(item)">
						<uni-icons type="compose" size="18" color="#999"></uni-icons>
						<text>编辑</text>
					</view>
					<view class="action-divider"></view>
					<view class="action-btn" @click="deleteAddress(item)">
						<uni-icons type="trash" size="18" color="#999"></uni-icons>
						<text>删除</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空地址状态 -->
		<view v-else class="empty-address">
			<image class="empty-image" src="https://via.placeholder.com/150x150/f8f9fc/3a559f?text=Empty" mode="aspectFit"></image>
			<text class="empty-text">暂无收货地址</text>
		</view>
		
		<!-- 添加新地址 -->
		<view class="add-address-btn" @click="addNewAddress">
			<uni-icons type="plus" size="20" color="#fff"></uni-icons>
			<text>添加新地址</text>
		</view>
	</view>
</template>

<script>
import { getAddressList, deleteAddress } from '@/utils/api.js';

export default {
	data() {
		return {
			addressList: [],
			fromOrder: false, // 是否从订单页面跳转而来
			selectedId: null, // 选中的地址ID
			loading: false // 加载状态
		}
	},
	onLoad(options) {
		// 判断是否从订单页面跳转而来
		if (options.from === 'order') {
			this.fromOrder = true;
		}
		
		if (options.selected) {
			this.selectedId = parseInt(options.selected);
		}
	},
	onShow() {
		// 每次显示页面时获取最新的地址列表
		this.loadAddressList();
	},
	methods: {
		// 加载地址列表
		loadAddressList() {
			if (this.loading) return;
			
			this.loading = true;
			uni.showLoading({
				title: '加载中...'
			});
			
			getAddressList().then(res => {
				this.loading = false;
				uni.hideLoading();
				
				if (res.code === 200) {
					this.addressList = res.data || [];
				} else {
					this.showToast(res.message || '获取地址列表失败');
				}
			}).catch(err => {
				this.loading = false;
				uni.hideLoading();
				this.showToast(err.message || '获取地址列表失败');
			});
		},
		
		// 添加新地址
		addNewAddress() {
			uni.navigateTo({
				url: '/pages/address/edit'
			});
		},
		
		// 编辑地址
		editAddress(item) {
			uni.navigateTo({
				url: `/pages/address/edit?id=${item.id}`
			});
		},
		
		// 删除地址
		deleteAddress(item) {
			uni.showModal({
				title: '提示',
				content: '确定要删除该地址吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '删除中...'
						});
						
						deleteAddress(item.id).then(res => {
							uni.hideLoading();
							
							if (res.code === 200) {
								// 从列表中移除被删除的地址
								const index = this.addressList.findIndex(address => address.id === item.id);
								if (index !== -1) {
									this.addressList.splice(index, 1);
								}
								
								this.showToast('删除成功', 'success');
							} else {
								this.showToast(res.message || '删除失败');
							}
						}).catch(err => {
							uni.hideLoading();
							this.showToast(err.message || '删除失败');
						});
					}
				}
			});
		},
		
		// 选择地址
		selectAddress(item) {
			if (!this.fromOrder) return;
			
			// 如果是从订单页面跳转而来，选择地址后返回
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];
			
			// 向上一页传递选择的地址
			if (prevPage && prevPage.$vm) {
				prevPage.$vm.setAddress(item);
			}
			
			uni.navigateBack();
		},
		
		// 显示提示信息
		showToast(title, icon = 'none') {
			uni.showToast({
				title: title,
				icon: icon
			});
		}
	}
}
</script>

<style>
.address-container {
	padding-bottom: 88px; /* 为底部固定按钮留出空间 */
}

.address-list {
	margin-top: 10px;
}

.address-item {
	margin: 0 10px 10px;
	border-radius: 8px;
	background-color: #fff;
	overflow: hidden;
}

.address-content {
	padding: 15px;
}

.address-info {
	display: flex;
	flex-direction: column;
}

.contact-info {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.name {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-right: 12px;
}

.phone {
	font-size: 16px;
	color: #333;
	margin-right: 12px;
}

.tag {
	padding: 0 5px;
	height: 20px;
	line-height: 20px;
	border-radius: 4px;
	background-color: #1a1a1a;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tag text {
	color: #fff;
	font-size: 12px;
}

.address-text {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.address-actions {
	display: flex;
	height: 50px;
	border-top: 1px solid rgba(58, 85, 159, 0.05);
}

.action-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 5px;
	color: #999;
	font-size: 14px;
}

.action-divider {
	width: 1px;
	height: 20px;
	background-color: rgba(58, 85, 159, 0.05);
	align-self: center;
}

.empty-address {
	padding: 100px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-image {
	width: 120px;
	height: 120px;
	margin-bottom: 20px;
}

.empty-text {
	font-size: 16px;
	color: #999;
}

.add-address-btn {
	position: fixed;
	left: 10px;
	right: 10px;
	bottom: 20px;
	height: 40px;
	background-color: #1a1a1a;
	color: #fff;
	font-size: 15px;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	z-index: 999;
}
</style> 