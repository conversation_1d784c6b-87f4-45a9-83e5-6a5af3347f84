<template>
    <view class="index-container">
        <!-- 自定义导航栏: 搜索栏(已包含状态栏适配) -->

        <!-- #ifdef APP-PLUS -->
        <view class="custom-nav">
            <view class="search-bar">
                <view class="brand-title">
                    <image class="logo" src="https://www.zhida.net/app-resource/icon/logo.png" mode="aspectFit"></image>
                </view>
                <view class="search-container">
                    <input class="search-input" readonly @click="goToSearch" placeholder="品牌 系列 型号"
                        placeholder-class="search-placeholder" />
                    <view class="search-button">
                        <uni-icons type="search" size="24" color="#fff"></uni-icons>
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->

        <!-- #ifdef MP-WEIXIN -->
        <view class="custom-nav" :style="navBarStyle">
            <view class="search-bar" :style="searchBarStyle">
                <view class="brand-title">
                    <image class="logo" src="https://www.zhida.net/app-resource/icon/logo.png" mode="aspectFit"></image>
                </view>
                <view class="search-container">
                    <input class="search-input" disabled @click="goToSearch" />
                    <view class="custom-placeholder">品牌 系列 型号</view>
                    <view class="search-button">
                        <uni-icons type="search" size="24" color="#fff"></uni-icons>
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 热门品牌 -->
            <view class="hot-brands-section">
                <view class="section-title">热销品牌</view>
                <view class="brand-grid">
                    <view class="brand-item" v-for="(brand, index) in hotBrands" :key="index"
                        @click="selectBrand(brand)">
                        <image class="brand-logo" :src="brand.brandLogo" mode="aspectFit"></image>
                        <text class="brand-name">{{ brand.name }}</text>
                    </view>
                </view>
            </view>

            <!-- 字母索引和品牌列表 -->
            <view class="alphabet-brands">
                <!-- 品牌列表 -->
                <view class="brand-list">
                    <view class="alphabet-section" v-for="(section, letter) in brandsByLetter" :key="letter"
                        :id="'letter-' + letter">
                        <view v-if="section.length > 0">
                            <view class="letter-header">{{ letter }}</view>
                            <view class="letter-brands">
                                <view class="letter-brand-item" v-for="(brand, idx) in section" :key="idx"
                                    @click="selectBrand(brand)">
                                    <image class="letter-brand-logo" :src="brand.brandLogo" mode="aspectFit"></image>
                                    <view class="letter-brand-name">{{ brand.name }}</view>
                                    <view class="letter-brand-name-en">{{ brand.brandEnglish }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 右侧字母索引导航 -->
                <view class="alphabet-nav" @touchstart="handleTouchStart" @touchmove="handleTouchMove"
                    @touchend="handleTouchEnd">
                    <view class="letter-nav-item" v-for="letter in alphabetLetters" :key="letter"
                        :class="{ active: currentLetter === letter }" @click="scrollToLetter(letter)">
                        {{ letter }}
                    </view>
                    <view class="letter-indicator" :class="{ visible: showIndicator }"
                        :style="{ top: indicatorTop + 'px' }">
                        {{ indicatorLetter }}
                    </view>
                </view>
            </view>
        </view>

        <!-- 固定底部导航栏 -->
        <BottomBar />
    </view>
</template>

<script>
import { getBrandList, getHotBrandList } from '@/utils/api.js'
import BottomBar from '@/components/BottomBar.vue'

export default {
    components: {
        BottomBar
    },
    data() {
        return {
            // 字母表
            alphabetLetters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
            // 热门品牌
            hotBrands: [],
            // 按字母分组的品牌
            brandsByLetter: {},
            // 字母导航相关
            currentLetter: '', // 当前选中的字母
            showIndicator: false, // 是否显示指示器
            indicatorLetter: '', // 指示器显示的字母
            indicatorTop: 0, // 指示器的top位置
            touchStartY: 0, // 触摸开始的Y坐标
            letterHeight: 20, // 每个字母的高度，与CSS中保持一致
            navRect: null, // 字母导航的位置信息
            // 胶囊按钮信息
            menuButtonInfo: {
                width: 0,
                height: 0,
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            },
            // 添加导航栏相关的数据
            navBarHeight: 0,
            statusBarHeight: 0,
            searchBarStyle: {},
            navBarStyle: {},
            contentStyle: {}
        }
    },
    onLoad() {
        // 获取热门品牌
        this.fetchHotBrands();

        // 获取品牌数据
        this.fetchBrands();

        // 获取字母导航的位置信息
        setTimeout(() => {
            this.getNavRect();
        }, 500);

        // #ifdef MP-WEIXIN
        this.getStatusBarHeight();
        // #endif
    },
    methods: {

        // 获取热门品牌
        async fetchHotBrands() {
            try {
                const res = await getHotBrandList();
                console.log(res);
                this.hotBrands = res.data || [];
            } catch (error) {
                uni.showToast({
                    title: error.message || '获取热门品牌失败',
                    icon: 'none'
                });
            }
        },

        // 获取品牌数据
        async fetchBrands() {
            try {
                const res = await getBrandList();
                console.log(res);
                this.brandsByLetter = res.data || {};
            } catch (error) {
                uni.showToast({
                    title: error.message || '获取品牌数据失败',
                    icon: 'none'
                });
            }
        },


        // 选择品牌
        selectBrand(brand) {
            uni.navigateTo({
                url: `/pages/product/list?brandId=${brand.id}`
            });
        },

        // 获取字母导航的位置信息
        getNavRect() {
            const query = uni.createSelectorQuery();
            query.select('.alphabet-nav').boundingClientRect(rect => {
                if (rect) {
                    this.navRect = rect;
                }
            }).exec();
        },

        // 处理触摸开始事件
        handleTouchStart(e) {
            this.touchStartY = e.touches[0].clientY;
            this.updateLetterByTouch(e.touches[0].clientY);
            this.showIndicator = true;
        },

        // 处理触摸移动事件
        handleTouchMove(e) {
            if (!this.navRect) return;

            e.preventDefault();
            e.stopPropagation();

            this.updateLetterByTouch(e.touches[0].clientY);
        },

        // 处理触摸结束事件
        handleTouchEnd() {
            // 300ms后隐藏指示器
            setTimeout(() => {
                this.showIndicator = false;
            }, 300);

            // 如果有当前字母，滚动到对应位置
            if (this.currentLetter) {
                this.scrollToLetter(this.currentLetter);
            }
        },

        // 根据触摸位置更新当前字母
        updateLetterByTouch(touchY) {
            if (!this.navRect) return;

            // 计算触摸的Y坐标相对于导航起始位置的偏移
            const offsetY = touchY - this.navRect.top;

            // 计算当前触摸的是第几个字母
            const index = Math.floor(offsetY / this.letterHeight);

            // 判断索引是否有效
            if (index >= 0 && index < this.alphabetLetters.length) {
                const letter = this.alphabetLetters[index];
                this.currentLetter = letter;
                this.indicatorLetter = letter;

                // 计算指示器位置，使其与当前触摸的字母保持在同一水平线上
                // 字母的中心位置 = 索引 * 高度 + 高度的一半 - 指示器高度的一半
                this.indicatorTop = index * this.letterHeight + (this.letterHeight / 2) - 8;
            }
        },

        // 滚动到指定字母区域
        scrollToLetter(letter) {
            this.currentLetter = letter;

            // 使用uni-app的节点查询API
            const query = uni.createSelectorQuery();
            query.select(`#letter-${letter}`).boundingClientRect();
            query.selectViewport().scrollOffset();
            query.exec(res => {
                if (res[0]) {
                    uni.pageScrollTo({
                        scrollTop: res[0].top + res[1].scrollTop - 100, // 100是顶部导航栏高度的估计值
                        duration: 0
                    });
                }
            });
        },

        // 跳转到搜索页面
        goToSearch() {
            uni.navigateTo({
                url: '/pages/search/search'
            });
        },

        // 获取状态栏高度和胶囊按钮信息
        getStatusBarHeight() {
            try {
                // 获取系统信息
                const systemInfo = uni.getSystemInfoSync();
                // 获取胶囊按钮的位置信息
                const menuButton = uni.getMenuButtonBoundingClientRect();
                
                // 保存状态栏高度
                this.statusBarHeight = systemInfo.statusBarHeight;
                
                // 保存胶囊按钮信息
                this.menuButtonInfo = {
                    width: menuButton.width,
                    height: menuButton.height,
                    top: menuButton.top,
                    right: menuButton.right,
                    bottom: menuButton.bottom,
                    left: menuButton.left
                };
                
                // 计算导航栏高度（胶囊按钮到顶部的距离 + 胶囊按钮的高度 + 5px的上下边距）
                const navBarHeight = (menuButton.top - systemInfo.statusBarHeight) * 2 + menuButton.height;
                this.navBarHeight = navBarHeight;
                
                // 计算搜索栏的宽度（屏幕宽度 - 右侧胶囊按钮占用的空间 - 左右边距）
                const searchWidth = systemInfo.windowWidth - (systemInfo.windowWidth - menuButton.left);
                
                // 设置搜索栏样式
                this.searchBarStyle = {
                    width: `${searchWidth}px`,
                    height: `${menuButton.height}px`,
                    marginTop: `${menuButton.top - systemInfo.statusBarHeight}px`,
                    marginRight: `${systemInfo.windowWidth - menuButton.left}px`,
                    paddingLeft: '10px'
                };
                
                // 设置导航栏样式
                this.navBarStyle = {
                    height: `${this.statusBarHeight + navBarHeight}px`,
                    paddingTop: `${this.statusBarHeight}px`,
                    paddingRight: `${systemInfo.windowWidth - menuButton.left}px`
                };
                
                // 更新内容区域的padding-top
                this.contentStyle = {
                    paddingTop: `${this.statusBarHeight + navBarHeight}px`
                };
                
                console.log('导航栏配置：', {
                    systemInfo,
                    menuButton,
                    navBarHeight,
                    searchWidth,
                    statusBarHeight: this.statusBarHeight,
                    searchBarStyle: this.searchBarStyle,
                    navBarStyle: this.navBarStyle,
                    contentStyle: this.contentStyle
                });
            } catch (error) {
                console.error('获取导航栏信息失败：', error);
            }
        }
    }
}
</script>

<style>
.index-container {
    padding: 0 0 60px 0;
    background-color: #fff;
    position: relative;
    min-height: 100vh;
}

/* 自定义导航栏 */

/* #ifdef APP-PLUS */
.custom-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    background-color: #fff;
    padding-top: var(--status-bar-height);
}
/* #endif */

/* #ifdef MP-WEIXIN */
.custom-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    background-color: #fff;
    box-sizing: border-box;
}

.search-bar {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    box-sizing: border-box;
}

.brand-title {
    display: flex;
    align-items: center;
}

.search-container {
    flex: 1;
    height: 32px;
    margin-right: 10px;
}

.content-container {
    width: 100%;
    box-sizing: border-box;
}

.custom-placeholder {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
    pointer-events: none;
    z-index: 1;
}
/* #endif */

.logo {
    width: 40px;
    height: 40px;
    margin-top: 5px;
}

/* 搜索栏样式 */
.search-bar {
    padding: 0px 15px;
    background-color: #fff;
    display: flex;
    align-items: center;
}

.brand-title {
    font-size: 22px;
    font-weight: bold;
    margin-right: 10px;
}

.search-container {
    flex: 1;
    display: flex;
    align-items: center;
    height: 36px;
    border-radius: 18px;
    padding: 0 0 0 15px;
    position: relative;
    border: 1.8px solid #1a1a1a;
    overflow: hidden;
}

.search-input {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    font-size: 14px;
    height: 100%;
}

.search-placeholder {
    color: #999;
    font-size: 14px;
}

.search-button {
    width: 50px;
    height: 32px;
    border-radius: 16px;
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 2px;
    top: 2px;
}

/* #ifdef MP-WEIXIN */
.content-container {
    padding-top: calc(var(--status-bar-height) + 48px);
    background-color: #fff;
}
/* #endif */

/* #ifdef APP-PLUS */
.content-container {
    padding-top: calc(var(--status-bar-height) + 40px);
    background-color: #fff;
}
/* #endif */

/* 热门品牌区域 */
.hot-brands-section {
    padding: 15px 15px 15px 0;
}

.section-title {
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    height: 28px;
    line-height: 28px;
    padding-left: 15px;
}

.brand-grid {
    display: flex;
    flex-wrap: wrap;
}

.brand-item {
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.brand-logo {
    width: 50px;
    height: 50px;
    margin-bottom: 8px;
}

.brand-name {
    font-size: 12px;
    color: #333;
    text-align: center;
}

/* 字母索引和品牌列表 */
.alphabet-brands {
    position: relative;
    display: flex;
}

.brand-list {
    flex: 1;
    padding: 0 15px 15px 0px;
    padding-right: 22px;
    /* 为右侧字母导航留出空间 */
}

.alphabet-section {
    margin-bottom: 15px;
}

.letter-header {
    height: 28px;
    line-height: 28px;
    background-color: #f5f5f5;
    color: #666;
    font-size: 12px;
    padding-left: 15px;
    font-weight: 500;
}

.letter-brands {
    padding: 10px 0 10px 15px;
}

.letter-brand-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
}

.letter-brand-logo {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.letter-brand-name {
    font-size: 14px;
    color: #333;
    margin-right: 10px;
}

.letter-brand-name-en {
    font-size: 12px;
    color: #999;
}

/* 右侧字母导航 */
.alphabet-nav {
    position: fixed;
    right: 2px;
    bottom: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0;
    z-index: 10;
    width: 16px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    /* 增强iOS滚动体验 */
    scrollbar-width: none;
    /* 隐藏滚动条 Firefox */
    gap: 2px;
}

.alphabet-nav::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 Chrome/Safari */
}

.letter-nav-item {
    font-size: 10px;
    color: #999;
    height: 18px;
    line-height: 18px;
    text-align: center;
    width: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.letter-nav-item.active {
    color: #fff;
    background-color: #1a1a1a;
    border-radius: 50%;
    font-weight: normal;
    /* 避免加粗导致体积变大 */
    width: 14px;
    height: 14px;
}

/* 索引导航中的当前圆形指示器 */
.letter-indicator {
    position: absolute;
    right: 0;
    width: 16px;
    height: 16px;
    background-color: #1a1a1a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 10px;
    z-index: 9;
    opacity: 0;
    transition: all 0.2s;
}

.letter-indicator.visible {
    opacity: 1;
}

/* 右侧字母导航样式调优 */
@media screen and (max-height: 600px) {
    .alphabet-nav {
        max-height: 90vh;
        /* 在较小屏幕上增加最大高度 */
    }

    .letter-nav-item {
        height: 12px;
        /* 在较小屏幕上减小高度 */
        line-height: 12px;
        font-size: 9px;
        /* 减小字体 */
    }
}

/* 为iOS设备优化滚动体验 */
@supports (-webkit-touch-callout: none) {
    .alphabet-nav {
        /* iOS设备特有样式 */
        padding-right: 2px;
    }
}
</style>