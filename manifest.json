{
    "name" : "值达",
    "appid" : "__UNI__0BE6EAA",
    "description" : "值达app-专注手表报表的应用",
    "versionName" : "1.1.3",
    "versionCode" : 113,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "safearea" : {
            "background" : "#ffffff",
            "bottom" : {
                "offset" : "auto"
            }
        },
        "softinputMode" : "adjustResize",
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Record" : {},
            "Camera" : {},
            "OAuth" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSMicrophoneUsageDescription" : "需要使用麦克风录制语音消息"
                },
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "univerify" : {}
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true,
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "d:/Users/<USER>/Downloads/res/drawable-hdpi/1080x23401.9.png",
                    "xhdpi" : "d:/Users/<USER>/Downloads/res/drawable-xhdpi/1080x23401.9.png",
                    "xxhdpi" : "d:/Users/<USER>/Downloads/res/drawable-xxhdpi/1080x23401.9.png"
                },
                "iosStyle" : "storyboard",
                "ios" : {
                    "storyboard" : "d:/Users/<USER>/Desktop/工作文档/CustomStoryboard.zip"
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxe158e04c7f5f83ba",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "app-harmony" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "h5" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-harmony" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-xhs" : {
        "uniStatistics" : {
            "enable" : false
        },
        "unipush" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : false
        }
    }
}
