<template>
  <view class="chart-wrap">
    <view class="line-chart-con">
      <l-echart class="line-chart" ref="lineChart"></l-echart>
    </view>
  </view>
</template>

<script>
import * as echarts from 'echarts/core'; 
import {
  LineChart
} from 'echarts/charts';
import {
  TooltipComponent,
  GridComponent,
} from 'echarts/components';
import {
  CanvasRenderer
} from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TooltipComponent,
  GridComponent,
  LineChart,
  CanvasRenderer
]);

export default {
  props: {
    chartData: {
      type: Object,
      required: true,
      default: () => ({
        xData: [],
        yData: []
      })
    }
  },
  data() {
    return {
      chart: null, // Store chart instance
      rendered: false
    };
  },
  mounted() {
    // Delay rendering to avoid unnecessary renderings
    this.$nextTick(() => {
      this.renderChart();
    });
  },
  // Clean up chart instance when component is destroyed
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        // Only re-render if data is actually different
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.renderChart();
        }
      },
      deep: true
    }
  },
  methods: {
    renderChart() {
      if (!this.$refs.lineChart) return;
      
      //这里option配置参考文档：https://echarts.apache.org/zh/option.html
      let option = {
        xAxis: {
          type: 'category',
          // x轴数据文字颜色
          axisLabel: {
            color: '#1a1a1a'
          },
          // x轴坐标轴线的颜色
          axisLine: {
            lineStyle: {
              color: '#f1f1f1',
            }
          },
          //x轴上面刻度线隐藏
          axisTick: {
            show: false,
          },
          //这里是x轴数据
          data: this.chartData.xData || []
        },
        //设置网格
        grid: {
          top: 10,
          bottom: 30,
          left: 45,
          right: 5
        },
        //y轴设置
        yAxis: {
          type: 'value',
          //y轴标签文字颜色
          axisLabel: {
            color: '#1a1a1a'
          },
          // y轴分割线设置为虚线
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        //设置提示为点击时
        tooltip: {
          trigger: 'axis',
          triggerOn: 'click',
          formatter: '{b} \n {c}'
        },
        //设置曲线的颜色
        color: ['#d6b391'],
        series: [{
          //这里是数据
          data: this.chartData.yData || [],
          type: 'line',
          //设置为平滑，默认为折线
          smooth: true,
          // Improve animation performance
          animation: true,
          animationDuration: 300,
          animationEasing: 'cubicOut'
        }]
      };

      this.$refs.lineChart.init(echarts, chart => {
        this.chart = chart; // Store chart instance
        chart.setOption(option);
        this.rendered = true;
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.chart-wrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: transparent;
  display: flex;
  flex-direction: column;

  .line-chart-con {
    width: 100%;
    height: 100%;
    flex: 1;
    box-sizing: border-box;
    
    .line-chart {
      width: 100%;
      height: 380rpx;
    }
  }
}
</style> 