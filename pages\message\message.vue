<template>
	<view class="message-container" :style="{ '--tab-bar-height': '50px' }">
		<!-- 遮罩层，当更多选项面板显示时可见 -->
		<view class="panel-mask" v-if="showMorePanel" @tap="hideMorePanel"></view>

		<!-- 录音组件 -->
		<m-recorder :value="showRecorder" @input="showRecorder = $event" :isCancel="isCancelRecording"
			@touchend="onRecordEnd"></m-recorder>

		<!-- 消息列表区域 -->
		<scroll-view class="message-list" :class="{ 'more-panel-visible': showMorePanel }" scroll-y="true"
			:scroll-into-view="scrollIntoView" :scroll-top="scrollTop" scroll-with-animation="true"
			ref="messageScrollView" id="messageScrollView">

			<!-- 消息内容 -->
			<view class="messages-wrapper">
				<!-- 无消息时的空状态 -->
				<!-- <view class="empty-message" v-if="messageList.length === 0">
					<text class="empty-text">您还没有任何消息</text>
					<text class="empty-subtext">向我们咨询任何关于腕表的问题</text>
				</view> -->

				<view class="message-item" v-for="(item, index) in messageList" :key="item.id || item.messageId"
					:id="`msg_${item.id || item.messageId}`">
					<view class="message" :class="item.sender === 'user' ? 'message-mine' : 'message-other'">
						<!-- 消息气泡 -->
						<view class="message-bubble">
							<!-- 头像和名称容器 -->
							<view class="sender-info">
								<image class="avatar" v-if="item.sender !== 'user'" :src="serviceAvatar"></image>
								<text class="sender-name" v-if="item.sender !== 'user'">{{ item.content.description ?
									'思考的结果' : '拼命思考中...' }}</text>
							</view>


							<!-- 文本消息 -->
							<view v-if="item.type === 'text'" class="message-content"
								:class="item.sender === 'user' ? 'content-mine' : 'content-other'">
								<text class="message-text">{{ item.content }}</text>
							</view>

							<!-- 图片消息 -->
							<view v-if="item.type === 'image'" class="message-content"
								:class="item.sender === 'user' ? 'content-mine' : 'content-other'">
								<image class="message-image" :src="item.content" mode="aspectFit"
									@tap="previewImage(item.content)" @load="onImageLoad"></image>
							</view>

							<!-- 语音消息 -->
							<view v-if="item.type === 'voice'" class="message-content"
								:class="item.sender === 'user' ? 'content-mine' : 'content-other'">
								<view class="voice-message" @tap="playVoice(item.address)">
									<view class="voice-playing">
										<uni-icons v-if="item.isPlaying" type="sound" size="16" color="#ffffff"
											class="voice-animate"></uni-icons>

										<uni-icons v-else="item.isPlaying" type="sound" size="16"
											color="#ffffff"></uni-icons>
									</view>
									<text class="voice-duration">{{ item.duration }}"</text>
								</view>
							</view>

							<!-- 图文消息 -->
							<view v-if="item.type === 'rich' && item.content.description" class="message-content"
								:class="item.sender === 'user' ? 'content-mine' : 'content-other'">
								<view class="rich-message">
									<text class="rich-text">{{ item.content.description }}</text>

									<view class="product-images-grid"
										v-if="item.content && item.content.watches && item.content.watches.length > 0">
										<view class="product-list-view">
											<view v-for="(li, index) in item.content.watches" :key="index"
												class="product-item">
												<view class="product-card">
													<view class="product-header" @tap="productDetail(li.watchesId)">
														<image class="product-thumbnail" :src="li.imageMain"
															mode="aspectFit" @load="onImageLoad"></image>
														<view class="product-info">
															<!-- 产品标题和型号行 -->
															<view class="product-title-row">
																<text class="product-title">{{ li.brand + ' ' +
																	li.series }}</text>
															</view>
															<!-- 编号和展开按钮行 -->
															<view class="reference-row">
																<text class="reference-number">{{ li.referenceNumber ||
																	'm1165001n-0001' }}</text>
																<view class="expand-btn"
																	@tap.stop="toggleExpand(li, index)">
																	<text class="expand-text">{{ li.expanded ? '收起' :
																		'展开' }}</text>
																	<uni-icons :type="li.expanded ? 'up' : 'down'"
																		size="14" color="#666"></uni-icons>
																</view>
															</view>

															<view class="price-row price-row-1">
																<text class="price-label">官方指导价</text>
																<text class="price-value">¥{{ li.officePrice }}</text>
															</view>
															<view class="price-row price-row-2">
																<text class="price-label">近期成交价</text>
																<text class="price-value">¥{{ li.latestUsedPrice
																}}</text>

																<text v-if="li.latestChangePercent"
																	:class="['price-value', parseFloat(li.latestChangePercent) < 0 ? 'price-down' : 'price-up']">
																	{{ li.latestChangePercent }}%
																</text>

																<image v-if="li.latestChangePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
																	mode="aspectFit"></image>
																<image v-if="li.latestChangePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
																	mode="aspectFit"></image>
															</view>
														</view>
													</view>

													<!-- 展开后显示的内容 -->
													<view class="expanded-content" v-if="li.expanded">
														<!-- 表盘参数标签行 -->
														<view class="spec-tags" @tap.stop="showSpecsSheet()">
															<view class="specs-left">
																<view class="spec-tag">
																	<text class="spec-value-text">{{
																		currentWatch.movement?.diameter || '--' }}</text>
																	<text class="spec-title-text">机芯直径</text>
																</view>
																<view class="spec-tag">
																	<text class="spec-value-text">{{
																		currentWatch.material?.caseMaterial || '--'
																	}}</text>
																	<text class="spec-title-text">表壳材质</text>
																</view>
																<view class="spec-tag">
																	<text class="spec-value-text">{{
																		currentWatch.appearance?.dialColor || '--'
																	}}</text>
																	<text class="spec-title-text">表盘颜色</text>
																</view>
																<view class="spec-tag">
																	<text class="spec-value-text">{{
																		currentWatch.movement?.movementType || '--'
																	}}</text>
																	<text class="spec-title-text">机芯类型</text>
																</view>
															</view>
															<view class="spec-arrow">
																<uni-icons type="right" size="14"
																	color="#999"></uni-icons>
															</view>
														</view>

														<!-- 行情走势部分 -->
														<view class="trend-section">
															<view class="trend-header">
																<text class="trend-title">行情走势</text>
																<view class="period-tabs">
																	<view class="period-tab"
																		v-for="(period, i) in ['90天', '180天', '1 年']"
																		:key="i"
																		:class="{ 'period-active': li.activePeriod === i }"
																		@tap.stop="setPeriod(li, i)">
																		<text>{{ period }}</text>
																	</view>
																</view>
															</view>

															<!-- 趋势图 -->
															<view class="trend-chart">
																<LineChart v-if="li.expanded && li.chartVisible"
																	:chartData="chartData" />
															</view>

															<!-- 价格统计 -->
															<view class="price-stats">
																<view class="stat-item">
																	<text class="stat-value">¥{{
																		currentWatch.watchesQuote.maxUsedPrice || 0
																	}}</text>
																	<text class="stat-label">最高行情</text>
																</view>
																<view class="stat-item">
																	<text class="stat-value">¥{{
																		currentWatch.watchesQuote.latestUsedPrice || 0
																	}}</text>
																	<text class="stat-label">当前行情</text>
																</view>
																<view class="stat-item">
																	<text class="stat-value">¥{{
																		currentWatch.watchesQuote.minUsedPrice || 0
																	}}</text>
																	<text class="stat-label">最低行情</text>
																</view>
															</view>

															<!-- 价格变化 -->
															<view class="price-stats">
																<view class="stat-item">
																	<text class="stat-value">¥{{
																		currentWatch.watchesQuote.originUsedPrice || 0
																	}}</text>
																	<text class="stat-label">初始行情</text>
																</view>
																<view class="stat-item">
																	<text class="stat-value">-¥{{
																		currentWatch.watchesQuote.riseAmount || 0
																	}}</text>
																	<text class="stat-label">涨跌金额</text>
																</view>
																<view class="stat-item">
																	<text class="stat-value">{{
																		currentWatch.watchesQuote.risePercent || 0
																	}}%</text>
																	<text class="stat-label">涨跌幅</text>
																</view>
															</view>

															<!-- 建议回收价 -->
															<view class="suggested-price">
																<view class="suggest-header">
																	<text class="suggest-title">值达建议回收价</text>
																	<uni-icons type="info" size="18"
																		color="#999"></uni-icons>
																</view>
																<view class="price-range">
																	<text class="range-value">¥{{
																		currentWatch.watchesQuote.maxUsedPrice || 0 }} ~
																		¥{{ currentWatch.watchesQuote.minUsedPrice || 0
																		}}</text>
																	<text class="range-date">更新时间 {{
																		currentWatch.watchesQuote.createTime || '--'
																	}}</text>
																</view>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 消息时间 -->
							<!-- <text class="message-time">{{ formatTime(item.time) }}</text> -->

							<!-- 点赞和踩功能 - 只对客服消息显示 -->
							<view class="feedback-buttons" v-if="item.sender != 'user'">
								<view class="feedback-button" @tap="likeMessage(item.id, item.liked)">
									<uni-icons type="hand-up-filled" size="18" color="#d6b391"
										v-if="item.liked"></uni-icons>
									<uni-icons type="hand-up" size="18" color="#1a1a1a" v-else></uni-icons>

									<!-- <uni-icons :type="item.liked ? 'hand-up-filled' : 'hand-up'" size="18"
										color="#1a1a1a"></uni-icons>
									<text v-if="item.likeCount && item.likeCount > 0" class="feedback-count">{{
										item.likeCount }}</text> -->
								</view>
								<view class="feedback-button" @tap="dislikeMessage(item.id, item.disliked)">
									<uni-icons type="hand-down-filled" size="18" color="#d6b391"
										v-if="item.disliked"></uni-icons>
									<uni-icons type="hand-down" size="18" color="#1a1a1a" v-else></uni-icons>

									<!-- <uni-icons :type="item.disliked ? 'hand-down-filled' : 'hand-down'" size="18"
										color="#1a1a1a"></uni-icons>
									<text v-if="item.dislikeCount && item.dislikeCount > 0" class="feedback-count">{{
										item.dislikeCount }}</text> -->
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 底部锚点元素，用于滚动至底部 -->
				<view id="msg_bottom" class="bottom-anchor"></view>
			</view>
		</scroll-view>

		<!-- 更多选项面板 - 移到input-container外面，避免被软键盘遮挡 -->
		<!-- <view class="more-panel" v-if="showMorePanel">
			<view class="more-item" @tap.stop="chooseImage">
				<view class="more-icon">
					<uni-icons type="image" size="24" color="#3a5199"></uni-icons>
				</view>
				<text class="more-text">图片</text>
			</view>

			<view class="more-item" @tap.stop="showSearch">
				<view class="more-icon">
					<uni-icons type="search" size="24" color="#3a5199"></uni-icons>
				</view>
				<text class="more-text">搜索</text>
			</view>
		</view> -->

		<!-- 输入框区域 -->
		<view class="input-container" :style="inputBarStyle">
			<view class="input-wrapper">
				<!-- 语音按钮 -->
				<view class="input-button voice-toggle" @tap="toggleVoiceInput">
					<!-- <uni-icons :type="isVoiceMode ? 'headphones' : 'mic'" size="22" color="#3a5199"></uni-icons> -->
					<image :src="isVoiceMode ? 'https://www.zhida.net/app-resource/images/key.png' : 'https://www.zhida.net/app-resource/images/audio1.png'" mode="aspectFit"
						class="voice-icon"></image>
				</view>

				<!-- 文本输入框 -->
				<view class="text-input-wrapper" v-if="!isVoiceMode">
					<input class="text-input" type="text" v-model="messageText" placeholder="请输入消息..."
						:focus="inputFocus" confirm-type="send" @confirm="sendMessage" @focus="onInputFocus"
						@blur="onInputBlur" />
				</view>

				<!-- 语音输入按钮 -->
				<view class="voice-input-button" v-if="isVoiceMode" @touchstart="startRecording"
					@touchmove="moveRecording" @touchend="endRecording" @touchcancel="cancelRecording">
					<text class="voice-button-text">{{ recordingText }}</text>
				</view>

				<!-- 更多按钮 -->
				<!-- <view class="input-button more-button" @tap="showMoreOptions">
					<uni-icons type="plus" size="24" color="#3a5199"></uni-icons>
				</view> -->

				<!-- 发送按钮 -->
				<view class="send-button" :class="{ 'send-active': messageText.trim().length > 0 }" @tap="sendMessage"
					v-if="!isVoiceMode">
					<text class="send-text">发送</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 点踩原因对话框 -->
	<view class="dislike-dialog-mask" v-if="showDislikeDialog" @tap="cancelDislike">
		<view class="dislike-dialog" @tap.stop>
			<view class="dislike-dialog-content">
				<view class="dislike-dialog-header">
					<view class="header-content">
						<text class="dislike-dialog-title">您对哪些方面不满意？</text>
						<text class="dislike-dialog-subtitle">您的反馈将帮助我们提供更好的服务</text>
					</view>
					<view class="close-btn" @tap="cancelDislike">
						<uni-icons type="close" size="22" color="#999"></uni-icons>
					</view>
				</view>
				<view class="dislike-reason-list">
					<view class="reason-tags">
						<view class="reason-tag" v-for="(reason, index) in dislikeReasons" :key="index"
							:class="{ 'reason-tag-selected': dislikeReason === reason }"
							@tap="selectDislikeReason(reason)">
							<text class="reason-tag-text">{{ reason }}</text>
						</view>
					</view>
				</view>
				<view class="dislike-dialog-input">
					<textarea class="dislike-textarea" v-model="dislikeReason" placeholder="请输入您不满意的原因，帮助我们改进服务..."
						maxlength="200" :adjust-position="true" :show-confirm-bar="false" :cursor-spacing="12"
						:fixed="true" style="height: auto;" @confirm="submitDislikeReason" />
					<view class="textarea-counter">{{ dislikeReason.length }}/200</view>
				</view>
				<view class="dislike-dialog-buttons">
					<view class="dislike-btn dislike-cancel-btn" @tap="cancelDislike">
						<text>取消</text>
					</view>
					<view class="dislike-btn dislike-submit-btn" @tap="submitDislikeReason">
						<text>提交</text>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 手表详情底部弹窗 -->
	<view class="specs-popup" v-if="showSpecsPopup" @tap.stop="closeSpecsPopup">
		<view class="specs-content" @tap.stop>
			<view class="specs-header">
				<text class="specs-title">腕表参数</text>
				<view class="close-specs" @tap="closeSpecsPopup">
					<uni-icons type="close" size="22" color="#666"></uni-icons>
				</view>
			</view>

			<scroll-view class="specs-list" scroll-y="true" show-scrollbar="false">
				<view class="specs-inner">
					<view class="specs-section">
						<text class="section-title">基本参数</text>
						<view class="spec-items-container">
							<view class="spec-item" v-for="(item, index) in basicSpecs" :key="index">
								<text class="spec-label">{{ item.label }}</text>
								<text class="spec-value">{{ item.value }}</text>
							</view>
						</view>
					</view>

					<view class="specs-section">
						<text class="section-title">手表参数</text>

						<view class="spec-items-container">
							<view class="spec-item" v-for="(item, index) in watchSpecs" :key="index">
								<text class="spec-label">{{ item.label }}</text>
								<text class="spec-value">{{ item.value }}</text>
							</view>
						</view>
					</view>

					<view class="specs-section">
						<text class="section-title">手表材质</text>

						<view class="spec-items-container">
							<view class="spec-item" v-for="(item, index) in watchMaterial" :key="index">
								<text class="spec-label">{{ item.label }}</text>
								<text class="spec-value">{{ item.value }}</text>
							</view>
						</view>
					</view>

					<view class="specs-section">
						<text class="section-title">手表外观</text>

						<view class="spec-items-container">
							<view class="spec-item" v-for="(item, index) in watchAppearance" :key="index">
								<text class="spec-label">{{ item.label }}</text>
								<text class="spec-value">{{ item.value }}</text>
							</view>
						</view>
					</view>

					<!-- 底部安全区域 -->
					<view class="safe-area-bottom"></view>
				</view>
			</scroll-view>
		</view>
	</view>

	<!-- 探测价格是否准确弹窗 -->
	<view class="price-check-dialog-mask" v-if="showPriceCheckDialog" @tap="closePriceCheckDialog">
		<view class="price-check-dialog" @tap.stop>
			<text class="dialog-title">估价是否准确</text>
			<image :src="productDetailResult.imageMain || ''" mode="aspectFit"
				class="watch-image"></image>
			<text class="watch-name">{{ productDetailResult.brand || '--' }} {{ productDetailResult.series || '--' }}</text>
			<text class="watch-reference">{{ productDetailResult.referenceNumber || '--' }}</text>
			<view class="watch-price-range">
				<text class="watch-price-range-text">¥{{ productDetailResult.watchesQuote.minUsedPrice || 0 }} ~ ¥{{ productDetailResult.watchesQuote.maxUsedPrice || 0 }}</text>
				<text class="watch-price-label">值达建议回收价</text>
			</view>
			<text class="feedback-hint">恳请您给出这次AI估价的反馈，您认为这次AI估价结果</text>
			<view class="action-buttons">
				<view class="action-button accurate" @tap="submitPriceCheck(1)">
					<text>价格准确</text>
				</view>
				<view class="action-button inaccurate" @tap="submitPriceCheck(0)">
					<text>价格不准</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
import mRecorder from './m-recorder.vue'
import { sendMsg, getChatMessageList, sendMessageFeedback, uploadFile, getProductDetail, checkPriceAccuracy } from '@/utils/api.js';
import LineChart from '@/components/LineChart.vue'

// Initialize recorder manager at the script level
const recorderManager = uni.getRecorderManager();

export default {
	components: {
		uniIcons,
		mRecorder,
		LineChart
	},
	data() {
		return {
			userAvatar: 'https://randomuser.me/api/portraits/men/75.jpg', // 用户头像
			serviceAvatar: 'https://www.zhida.net/app-resource/icon/im.png', // 客服头像
			messageList: [], // 消息列表
			messageText: '', // 输入的文本消息
			userId: '', // 当前用户ID
			inputFocus: false, // 输入框焦点
			showMorePanel: false, // 是否显示更多选项面板
			scrollIntoView: '', // 滚动到指定元素ID
			scrollTimer: null, // 滚动定时器
			keyboardVisible: false, // 软键盘是否可见
			keyboardHeight: 0, // 软键盘高度
			showDislikeDialog: false, // 显示点踩原因对话框
			dislikeReason: '', // 点踩原因
			currentDislikeMessageId: null, // 当前点踩的消息ID
			dislikeReasons: ['信息不准确', '回复不相关', '态度不好', '解决方案不满意', '其他原因'], // 预设点踩原因
			// 语音相关变量
			isVoiceMode: false, // 是否为语音输入模式
			showRecorder: false, // 是否显示录音界面
			isCancelRecording: false, // 是否取消录音
			recorderStartY: 0, // 录音开始时的Y坐标
			recordingText: '按住说话', // 录音按钮文本
			recorderTimer: null, // 录音计时器
			recorderDuration: 0, // 录音时长
			currentVoiceMessageId: null, // 当前正在播放的语音消息ID
			voicePlayer: null, // 语音播放器实例
			inputBarStyle: {},

			// 新增变量
			showSpecsPopup: false, // 是否显示手表详情弹窗
			basicSpecs: [], // 基本参数
			watchSpecs: [], // 手表参数
			watchMaterial: [], // 手表材质
			watchAppearance: [], // 手表外观
			currentWatch: {	// 当前展开选中的手表
				movement: {},
				material: {},
				appearance: {},
				watchesQuote: {}
			},
			chartData: {
				xData: [], // x轴数据
				yData: [] // y轴数据
			},
			currentPeriod: 0, // 当前选择的周期
			scrollTop: 0, // 滚动位置
			scrollDebounceTimer: null, // 滚动防抖定时器

			showPriceCheckDialog: false, // 控制弹窗显示
			productDetailResult: {watchesQuote: {minUsedPrice: 0, maxUsedPrice: 0}}, // 产品详情
		};
	},
	onLoad() {
		console.log('=== onLoad ===');
		this.initMessages();
	},
	onShow() {
		console.log('=== onShow ===');

		// 获取产品id
		const app = getApp();
		const productId = app.globalData.productId;
		if(productId) {
			console.log('=== 产品id ===', productId);
			if(app.globalData.productDetailStayTime > 30) 
			{
				getProductDetail(productId)
					.then(res => {
						console.log(res);
						this.productDetailResult = res.data;
						this.showPriceCheckDialog = true;
					})
					.catch(err => {
						console.log(err);
					});
			}

			// 清除产品id
			app.globalData.productId = null;
		}

		const userInfo = uni.getStorageSync('userInfo');
		this.userAvatar = userInfo.avatar;

		// 重置输入框状态和位置
		this.keyboardVisible = false;
		this.keyboardHeight = 0;

		// 重置输入框样式为初始状态
		this.resetInputBarPosition();

		// 监听键盘高度变化
		uni.onKeyboardHeightChange(res => {
			this.keyboardHeight = res.height;
			// 当键盘高度大于0时，设置keyboardVisible为true
			this.keyboardVisible = res.height > 0;

			// 键盘弹出时，调整输入框位置，添加10px边距
			if (res.height > 0) {
				this.showMorePanel = false;
				// 添加10px的边距
				this.inputBarStyle = {
					bottom: '6px', // 添加10px的间距
					position: 'fixed'
				};
			} else {
				// 键盘收起，重置输入框位置
				this.resetInputBarPosition();
			}
		});

		// 初始化录音相关监听
		this.initRecorderListeners();
	},

	// 页面隐藏时重置键盘状态和输入框位置
	onHide() {
		this.keyboardVisible = false;
		this.keyboardHeight = 0;
		// 确保键盘收起
		uni.hideKeyboard();
		// 重置输入框位置
		this.resetInputBarPosition();
	},

	// 页面卸载时清理资源
	onUnload() {
		this.keyboardVisible = false;
		this.keyboardHeight = 0;
		// 清除键盘监听
		uni.offKeyboardHeightChange(function (res) { console.log(res) });
		// 清除滚动相关定时器
		if (this.scrollTimer) {
			clearTimeout(this.scrollTimer);
		}
		if (this.scrollDebounceTimer) {
			clearTimeout(this.scrollDebounceTimer);
		}
		// 重置输入框位置
		this.resetInputBarPosition();
	},
	methods: {
		// 重置输入框位置方法
		resetInputBarPosition() {
			// 使用基本样式，不添加额外边距
			this.inputBarStyle = {
				bottom: '0px',
				position: 'fixed',
				// paddingBottom: 'calc(env(safe-area-inset-bottom) + 4px)'
			};
		},

		// 初始化消息列表
		async initMessages() {
			try {
				await this.fetchAndUpdateMessages();
			} catch (err) {
				uni.showToast({
					title: '消息列表获取失败',
					icon: 'none'
				});
			}
		},

		// 获取消息列表
		async fetchAndUpdateMessages() {
			try {
				const res = await getChatMessageList();
				console.log(res);

				this.messageList = res.rows;

				// 确保DOM完全渲染后再滚动到底部
				this.$nextTick(() => {
					// 立即尝试滚动到底部
					this.scrollTop = 999999;
					this.scrollIntoView = 'msg_bottom';

					// 使用防抖滚动，减少冲突
					this.debouncedScrollToBottom();
				});

				return res.rows;
			} catch (err) {
				console.error('Failed to fetch messages:', err);
				throw err;
			}
		},

		// 防抖滚动到底部
		debouncedScrollToBottom() {
			// 清除之前的防抖定时器
			if (this.scrollDebounceTimer) {
				clearTimeout(this.scrollDebounceTimer);
			}

			this.scrollDebounceTimer = setTimeout(() => {
				this.scrollToBottom();
			}, 100);
		},

		// 滚动到底部
		scrollToBottom() {
			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}

			this.scrollTimer = setTimeout(() => {
				// 使用查询选择器获取scroll-view和内容的高度
				const query = uni.createSelectorQuery().in(this);
				query.select('#messageScrollView').boundingClientRect();
				query.select('.messages-wrapper').boundingClientRect();
				query.exec((res) => {
					if (res && res[0] && res[1]) {
						const scrollViewHeight = res[0].height;
						const contentHeight = res[1].height;

						// 计算需要滚动的距离（内容高度 - 可视区域高度）
						const maxScrollTop = Math.max(0, contentHeight - scrollViewHeight + 100); // 增加缓冲区到100px

						console.log('滚动信息:', {
							scrollViewHeight,
							contentHeight,
							maxScrollTop,
							currentScrollTop: this.scrollTop
						});

						// 只有当计算出的滚动位置大于当前位置时才更新
						if (maxScrollTop > this.scrollTop) {
							this.scrollTop = maxScrollTop;
						}

						// 备用方案：使用scroll-into-view，但减少频率
						setTimeout(() => {
							this.scrollIntoView = 'msg_bottom';
							setTimeout(() => {
								this.scrollIntoView = '';
							}, 50); // 减少重置时间
						}, 100); // 减少延迟时间
					} else {
						// 如果获取高度失败，使用一个很大的数值强制滚动到底部
						this.scrollTop = Math.max(this.scrollTop, 999999);
						this.scrollIntoView = 'msg_bottom';
						setTimeout(() => {
							this.scrollIntoView = '';
						}, 50);
					}
				});
			}, 50); // 减少延迟时间
		},

		// 发送文本消息
		async sendMessage() {
			if (this.messageText.trim().length === 0) return;

			const newMessage = {
				messageId: Date.now(),
				sender: 'user',
				type: 'text',
				content: this.messageText,
			};

			this.messageList.push(newMessage, {
				messageId: Date.now(),
				sender: 'assistant',
				type: 'rich',
				content: {
					description: '',
				},
			});
			this.messageText = '';

			// 使用$nextTick确保DOM更新后再滚动
			this.$nextTick(() => {
				// 立即设置滚动到底部，避免延迟
				this.scrollTop = 999999;
				this.scrollIntoView = 'msg_bottom';

				// 使用防抖滚动
				this.debouncedScrollToBottom();
			});

			try {
				const res = await sendMsg(newMessage);
				console.log(res);

				// 把最后一条消息替换为res.data[1]
				this.messageList[this.messageList.length - 1] = res.data[1];

				// 确保内容更新后再次滚动到底部
				this.$nextTick(() => {
					this.debouncedScrollToBottom();
				});

			} catch (err) {
				uni.showToast({
					title: '消息发送失败',
					icon: 'none'
				});
				console.log(err);

				// 失败时也需要滚动到底部
				this.$nextTick(() => {
					this.debouncedScrollToBottom();
				});
			}
		},

		// 发送图片消息
		sendImageMessage(filePath) {
			const newMessage = {
				id: Date.now(),
				sender: 'user',
				type: 'image',
				content: filePath,
				time: Date.now(),
			};

			this.messageList.push(newMessage);

			// 关闭更多面板
			this.showMorePanel = false;

			// 使用$nextTick确保DOM更新后再滚动
			this.$nextTick(() => {
				// 立即设置滚动到底部，避免延迟
				this.scrollTop = 999999;
				this.scrollIntoView = 'msg_bottom';

				// 使用防抖滚动
				this.debouncedScrollToBottom();
			});
		},

		// 图片加载完成后处理
		onImageLoad() {
			// 图片加载完成后，内容高度可能发生变化，需要重新滚动到底部
			this.$nextTick(() => {
				this.debouncedScrollToBottom();
			});
			console.log('图片加载完成');
		},

		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1,
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					this.sendImageMessage(tempFilePath);
				},
				fail: () => {
					console.log('fail');
				}
			});
		},

		// 预览图片
		previewImage(url) {
			uni.previewImage({
				urls: [url],
				current: url
			});
		},

		// 跳转产品详情
		productDetail(id) {

			// 全局存储产品id
			const app = getApp();
			app.globalData.productId = id;

			uni.navigateTo({
				url: `/pages/product/detail?id=${id}`
			});
		},
		// 显示更多选项
		showMoreOptions() {
			// 如果键盘已显示，需要先隐藏键盘
			if (this.keyboardVisible) {
				uni.hideKeyboard();

				// 延迟显示更多面板，确保键盘已收起
				setTimeout(() => {
					this.showMorePanel = !this.showMorePanel;
				}, 150);
			} else {
				// 直接切换面板状态
				this.showMorePanel = !this.showMorePanel;
			}
		},

		// 隐藏更多选项面板
		hideMorePanel() {
			this.showMorePanel = false;
		},

		// 跳转到搜索页面
		showSearch() {
			uni.navigateTo({
				url: '/pages/search/search',
			});
			this.showMorePanel = false;
		},

		// 输入框获得焦点
		onInputFocus() {
			// 立即关闭更多面板，防止被软键盘遮挡
			this.showMorePanel = false;
			this.keyboardVisible = true;

			// 键盘弹出时，确保视图滚动到底部
			this.$nextTick(() => {
				setTimeout(() => {
					this.debouncedScrollToBottom();
				}, 300);
			});
		},

		// 输入框失去焦点
		onInputBlur() {
			// 延迟设置键盘状态，避免界面闪烁
			setTimeout(() => {
				this.keyboardVisible = false;
				// 重置输入框位置
				this.resetInputBarPosition();
			}, 100);
		},

		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			if (date.toDateString() === now.toDateString()) {
				return this.formatTimeOnly(date);
			}
			const yesterday = new Date(now);
			yesterday.setDate(now.getDate() - 1);
			if (date.toDateString() === yesterday.toDateString()) {
				return `昨天 ${this.formatTimeOnly(date)}`;
			}
			return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTimeOnly(date)}`;
		},

		// 格式化时间（只有时分）
		formatTimeOnly(date) {
			const hours = date.getHours();
			const minutes = date.getMinutes();
			return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
		},

		// 点赞消息
		async likeMessage(id) {
			const messageIndex = this.messageList.findIndex(item => item.id === id);
			if (messageIndex !== -1) {
				const message = this.messageList[messageIndex];

				// 点赞/取消
				if (message.liked) {
					this.messageList[messageIndex].liked = 0;
					this.messageList[messageIndex].feedbackContent = '';
				} else {
					this.messageList[messageIndex].liked = 1;
					this.messageList[messageIndex].disliked = 0;
					this.messageList[messageIndex].feedbackContent = '';
				}

				try {
					await sendMessageFeedback(this.messageList[messageIndex]);
				} catch (error) {
					console.error('点赞失败:', error);
				}
			}
		},

		// 点踩消息
		async dislikeMessage(id) {

			const messageIndex = this.messageList.findIndex(item => item.id === id);
			if (messageIndex !== -1) {
				const message = this.messageList[messageIndex];

				// 点踩/取消
				if (message.disliked) {
					this.messageList[messageIndex].disliked = 0;
					this.messageList[messageIndex].feedbackContent = '';

					try {
						await sendMessageFeedback(this.messageList[messageIndex]);
					} catch (error) {
						console.error('点赞失败:', error);
					}

				} else {
					this.showDislikeDialog = true;
					this.currentDislikeMessageId = id;
				}
			}
		},

		// 选择预设点踩原因
		selectDislikeReason(reason) {
			this.dislikeReason = reason;
		},

		// 取消点踩
		cancelDislike() {
			this.showDislikeDialog = false;
			this.dislikeReason = '';
			this.currentDislikeMessageId = null;
		},

		// 提交点踩原因
		async submitDislikeReason() {
			if (!this.dislikeReason.trim()) {
				uni.showToast({
					title: '请输入不满意的原因',
					icon: 'none'
				});
				return;
			}

			const messageIndex = this.messageList.findIndex(item => item.id === this.currentDislikeMessageId);
			if (messageIndex !== -1) {
				// 如果已经点赞，则先取消点赞
				if (this.messageList[messageIndex].liked) {
					this.messageList[messageIndex].liked = 0;
				}

				// 点踩
				this.messageList[messageIndex].disliked = 1;
				this.messageList[messageIndex].feedbackContent = this.dislikeReason;

				try {
					await sendMessageFeedback(this.messageList[messageIndex]);
				} catch (error) {
					console.error('点赞失败:', error);
				}
			}

			// 关闭对话框
			this.showDislikeDialog = false;
			this.dislikeReason = '';
			this.currentDislikeMessageId = null;
		},


		// 切换语音/文本输入模式
		toggleVoiceInput() {
			this.isVoiceMode = !this.isVoiceMode;
			if (this.isVoiceMode) {
				this.hideMorePanel();
				uni.hideKeyboard();
			} else {
				this.recordingText = '按住说话';
				// Auto focus the input when switching to text mode
				this.inputFocus = true;
				// Allow some time for the UI to update before focusing
				setTimeout(() => {
					this.inputFocus = true;
				}, 100);
			}
		},

		// 开始录音
		startRecording(e) {
			// 停止任何正在播放的语音
			if (this.voicePlayer) {
				this.voicePlayer.stop();

				// 重置正在播放的消息状态
				if (this.currentVoiceMessageId) {
					const prevMsgIndex = this.messageList.findIndex(item => item.id === this.currentVoiceMessageId);
					if (prevMsgIndex !== -1) {
						this.messageList[prevMsgIndex].isPlaying = false;
					}
				}

				this.currentVoiceMessageId = null;
				this.voicePlayer = null;
			}

			console.log('开始录音');
			this.recorderStartY = e.touches[0].clientY;
			this.showRecorder = true; // 显示录音界面
			this.isCancelRecording = false;
			this.recordingText = '松开发送';
			this.recorderDuration = 0;

			// 开始录音
			recorderManager.start({
				duration: 60000, // 最长录音时间，单位ms
				format: 'mp3', // 录音的格式
				sampleRate: 16000, // 采样率
				numberOfChannels: 1, // 录音通道数
				encodeBitRate: 96000, // 编码码率
				frameSize: 50 // 指定帧大小
			});

			// 开始录音计时
			this.recorderTimer = setInterval(() => {
				this.recorderDuration++;
				if (this.recorderDuration >= 60) {
					this.endRecording();
				}
			}, 1000);
		},

		// 录音过程中的触摸移动
		moveRecording(e) {
			if (!this.showRecorder) return;
			console.log('录音移动', e.touches[0].clientY);
			const moveY = e.touches[0].clientY;
			const moveDistance = this.recorderStartY - moveY;

			// 上滑一定距离则显示取消录音状态
			if (moveDistance > 50) {
				this.isCancelRecording = true;
				this.recordingText = '松开取消';
			} else {
				this.isCancelRecording = false;
				this.recordingText = '松开发送';
			}
		},

		// 结束录音
		endRecording() {
			console.log('结束录音', this.showRecorder);
			if (!this.showRecorder) return;

			clearInterval(this.recorderTimer);
			this.showRecorder = false;
			this.recordingText = '按住说话';

			// 停止录音
			recorderManager.stop();
		},

		// 取消录音
		cancelRecording() {
			console.log('取消录音 (touchcancel)');
			if (!this.showRecorder) return;

			clearInterval(this.recorderTimer);
			this.showRecorder = false;
			this.recordingText = '按住说话';
			this.isCancelRecording = true;
			recorderManager.stop();
		},

		// 录音组件触发结束
		onRecordEnd() {
			console.log('录音组件触发结束');
			// 确保手动结束录音
			this.endRecording();
		},

		// 播放语音消息
		playVoice(filePath) {
			console.log(filePath);

			// 如果有正在播放的语音，先停止它
			if (this.voicePlayer) {
				this.voicePlayer.stop();

				// 重置之前的消息播放状态
				const prevMsgIndex = this.messageList.findIndex(item => item.address === this.currentVoiceMessageId);
				if (prevMsgIndex !== -1) {
					this.messageList[prevMsgIndex].isPlaying = false;
				}
			}

			// 查找当前要播放的消息
			const msgIndex = this.messageList.findIndex(item => item.address === filePath);
			if (msgIndex !== -1) {
				this.messageList[msgIndex].isPlaying = true;
			}

			// 记录当前播放的消息ID
			this.currentVoiceMessageId = filePath;

			// 创建播放器实例
			const audioContext = uni.createInnerAudioContext();
			this.voicePlayer = audioContext;
			audioContext.src = filePath;
			audioContext.play();

			audioContext.onEnded(() => {
				console.log('音频播放结束');
				// 播放结束时重置状态
				if (msgIndex !== -1) {
					this.messageList[msgIndex].isPlaying = false;
				}
				this.currentVoiceMessageId = null;
				this.voicePlayer = null;
			});

			audioContext.onError((res) => {
				console.error('播放错误', res);
				// 出错时也重置状态
				if (msgIndex !== -1) {
					this.messageList[msgIndex].isPlaying = false;
				}
				this.currentVoiceMessageId = null;
				this.voicePlayer = null;
			});
		},

		// 初始化录音相关监听
		initRecorderListeners() {
			// 监听录音开始事件
			recorderManager.onStart(() => {
				console.log('录音开始');
			});

			// 监听录音停止事件
			recorderManager.onStop((res) => {
				console.log('录音结束', res);
				// 录音时间太短
				if (this.recorderDuration < 1) {
					uni.showToast({
						title: '录音时间太短',
						icon: 'none'
					});
					return;
				}

				// 如果不是取消，则发送语音消息
				if (!this.isCancelRecording) {
					this.sendVoiceMessage(res.tempFilePath, this.recorderDuration);
				}
			});

			// 监听录音错误事件
			recorderManager.onError((res) => {
				console.error('录音错误:', res);
				uni.showToast({
					title: '录音失败，请检查麦克风权限',
					icon: 'none'
				});

				this.showRecorder = false;
				this.recordingText = '按住说话';
			});
		},

		// 发送语音消息
		async sendVoiceMessage(filePath, duration) {
			const newMessage = {
				id: Date.now(),
				sender: 'user',
				type: 'voice',
				content: filePath,
				address: filePath,
				duration: duration, // 语音时长（秒）
				time: Date.now(),
				isPlaying: false,
			};

			this.messageList.push(newMessage, {
				messageId: Date.now(),
				sender: 'assistant',
				type: 'rich',
				content: {
					description: '',
				},
			});

			// 使用$nextTick确保DOM更新后再滚动
			this.$nextTick(() => {
				// 立即设置滚动到底部，避免延迟
				this.scrollTop = 999999;
				this.scrollIntoView = 'msg_bottom';

				// 使用防抖滚动
				this.debouncedScrollToBottom();
			});

			try {
				const res = await uploadFile(filePath);
				let parsedData = res;
				if (typeof res === 'string') {
					parsedData = JSON.parse(res);
				}
				const result = await sendMsg({
					messageId: Date.now(),
					sender: 'user',
					content: parsedData.fileName,
					duration: duration,
					type: 'voice',
				});
				console.log(result);

				this.messageList[this.messageList.length - 1] = result.data[1];

				// 确保内容更新后再次滚动到底部
				this.$nextTick(() => {
					this.debouncedScrollToBottom();
				});

			} catch (error) {
				console.log('语音上传失败:', error);

				// 失败时也需要滚动到底部
				this.$nextTick(() => {
					this.debouncedScrollToBottom();
				});
			}
		},

		// 格式化价格显示
		formatPrice(price) {
			if (!price || price === '--') return '--';

			// 如果价格字符串中已经包含逗号，先移除它们
			if (typeof price === 'string') {
				price = price.replace(/,/g, '');
			}

			// 将价格转换为数字
			const numPrice = parseFloat(price);
			if (isNaN(numPrice)) return price;

			// 格式化为千分位分隔的数字
			return numPrice.toLocaleString('zh-CN');
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '--';

			const date = new Date(timestamp);
			if (isNaN(date.getTime())) return timestamp;

			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
		},

		// 切换展开/收起手表详情
		async toggleExpand(watch, index) {

			// 关闭其他已展开的手表图表
			this.messageList.forEach(item => {
				if (item.type === 'rich' && item.content && item.content.watches && item.content.watches.length > 0) {
					item.content.watches.forEach(w => {
						if (w !== watch && w.expanded) {
							w.expanded = false;
							w.chartVisible = false;
						}
					});
				}
			});

			// 切换当前手表的展开状态
			watch.expanded = !watch.expanded;

			// 如果展开，设置图表可见性并延迟初始化图表
			if (watch.expanded) {

				// 获取手表数据
				const currentWatch = await getProductDetail(watch.watchesId);
				console.log(currentWatch);
				this.currentWatch = currentWatch.data;

				// 延迟执行，确保DOM已渲染
				setTimeout(() => {
					watch.chartVisible = true;
					// 设置图表数据
					this.setPeriod(watch, watch.activePeriod || 0);
				}, 50);
			} else {
				watch.chartVisible = false;
			}
		},

		// 设置时间段
		setPeriod(watch, period) {
			watch.activePeriod = period;

			// 更新当前选择的周期并重新处理图表数据
			this.currentPeriod = period;
			this.processChartData();
		},

		// 关闭手表详情弹窗
		closeSpecsPopup() {
			this.showSpecsPopup = false;
		},

		// 生成规格数据
		generateSpecsData() {
			// 基本参数
			this.basicSpecs = [
				{ label: '手表品牌', value: this.currentWatch.brand || '--' },
				{ label: '手表编号', value: this.currentWatch.referenceNumber || '--' },
				{ label: '手表系列', value: this.currentWatch.series || '--' },
				{ label: '适用性别', value: this.currentWatch.gender || '--' },
			];

			console.log(this.currentWatch);

			// 机芯参数
			this.watchSpecs = [
				{ label: '机芯类型', value: this.currentWatch.movement?.movementType || '--' },
				{ label: '机芯直径', value: this.currentWatch.movement?.diameter || '--' },
				{ label: '厚度', value: this.currentWatch.movement?.thickness || '--' },
				{ label: '摆轮类型', value: this.currentWatch.movement?.balanceWheel || '--' },
				{ label: '振频', value: this.currentWatch.movement?.frequency || '--' },
				{ label: '游丝类型', value: this.currentWatch.movement?.hairspring || '--' },
				{ label: '避震', value: this.currentWatch.movement?.shockAbsorption || '--' },
				{ label: '宝石数', value: this.currentWatch.movement?.jewels || '--' },
				{ label: '零件数', value: this.currentWatch.movement?.components || '--' },
				{ label: '动力储备', value: this.currentWatch.movement?.powerReserve || '--' },
				{ label: '技术认证', value: this.currentWatch.movement?.certification || '--' }
			];

			// 手表材质
			this.watchMaterial = [
				{ label: '表壳材质', value: this.currentWatch.material?.caseMaterial || '--' },
				{ label: '表盘材质', value: this.currentWatch.material?.dialMaterial || '--' },
				{ label: '表圈材质', value: this.currentWatch.material?.bezelMaterial || '--' },
				{ label: '表镜材质', value: this.currentWatch.material?.crystalMaterial || '--' },
				{ label: '表冠材质', value: this.currentWatch.material?.crownMaterial || '--' },
				{ label: '表带材质', value: this.currentWatch.material?.bandMaterial || '--' },
				{ label: '表扣材质', value: this.currentWatch.material?.claspMaterial || '--' }
			];

			// 手表外观
			this.watchAppearance = [
				{ label: '手表编号', value: this.currentWatch.appearance?.referenceNumber || '--' },
				{ label: '表壳直径', value: this.currentWatch.appearance?.caseDiameter || '--' },
				{ label: '表壳厚度', value: this.currentWatch.appearance?.caseThickness || '--' },
				{ label: '表盘颜色', value: this.currentWatch.appearance?.dialColor || '--' },
				{ label: '表盘形状', value: this.currentWatch.appearance?.dialShape || '--' },
				{ label: '表带颜色', value: this.currentWatch.appearance?.bandColor || '--' },
				{ label: '表扣类型', value: this.currentWatch.appearance?.claspType || '--' },
				{ label: '表底类型', value: this.currentWatch.appearance?.caseBack || '--' },
				{ label: '手表重量', value: this.currentWatch.appearance?.weight || '--' },
				{ label: '防水性能', value: this.currentWatch.appearance?.waterResistance || '--' },
				{ label: '时标类型', value: this.currentWatch.appearance?.hourMarker || '--' },
				{ label: '表扣间距', value: this.currentWatch.appearance?.claspSpacing || '--' },
				{ label: '表耳间距', value: this.currentWatch.appearance?.lugSpacing || '--' }
			];

			// 行情走势数据处理
			this.processChartData();
		},

		// 行情走势数据处理
		processChartData() {
			if (!this.currentWatch.watchesQuoteCount || !Array.isArray(this.currentWatch.watchesQuoteCount)) {
				// 如果没有数据，清空图表
				this.chartData = {
					xData: [],
					yData: []
				};
				return;
			}

			const trendData = this.currentWatch.watchesQuoteCount;

			// 根据当前选择的周期过滤数据
			const currentPeriod = this.getCurrentPeriod();
			const filteredData = trendData.filter(item => item.period === currentPeriod);

			if (filteredData.length === 0) {
				// 如果当前周期没有数据，清空图表
				this.chartData = {
					xData: [],
					yData: []
				};
				return;
			}

			// 按月份排序（从早到晚）
			filteredData.sort((a, b) => new Date(a.month) - new Date(b.month));

			// 生成图表数据
			const xData = filteredData.map(item => {
				const date = new Date(item.month);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
			});

			const yData = filteredData.map(item => item.avgPrice || 0);

			this.chartData = {
				xData: xData,
				yData: yData
			};

			console.log(`当前周期: ${currentPeriod}, 数据点数量: ${filteredData.length}`, this.chartData);
		},

		// 获取当前选择的周期
		getCurrentPeriod() {
			// 根据当前选择的周期返回对应的period值
			switch (this.currentPeriod) {
				case 0: // 90天
					return '90d';
				case 1: // 180天
					return '180d';
				case 2: // 1年
					return '365d';
				default:
					return '90d';
			}
		},

		// 展示手表规格
		showSpecsSheet() {
			this.showSpecsPopup = true;
			this.generateSpecsData();
		},

		// 图表点击选中事件处理
		onPointSelected(point) {
			console.log('Selected point:', point);
			// 这里可以处理点击图表上的点时的逻辑
			// 例如显示该时间点的详细价格信息
			uni.showToast({
				title: `${this.formatDate(point.date)}: ¥${this.formatPrice(point.price)}`,
				icon: 'none'
			});
		},

		// 关闭价格检查弹窗
		closePriceCheckDialog() {
			this.showPriceCheckDialog = false;
		},

		// 提交价格检查
		submitPriceCheck(is_exact) {
			this.showPriceCheckDialog = false;
			checkPriceAccuracy({
				watchesId: this.productDetailResult.id,
				isExact: is_exact
			}).then(res => {
				console.log(res);
				uni.showToast({
					title: res.message,
					icon: 'none'
				});
			}).catch(err => {
				console.log(err);
			});
		},
	},
};
</script>

<style>
.message-container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f7f7f7;
	position: relative;
	overflow: hidden;
}

/* 消息列表区域 */
.message-list {
	flex: 1;
	/* 使用固定的底部空间，不再动态改变 */
	padding-bottom: 51px;
	/* 输入框高度 + 安全距离 */
	height: calc(100vh - 44px - var(--status-bar-height));
	background-color: #f7f7f7;
	position: relative;
	-webkit-overflow-scrolling: touch;
}

/* 当更多面板显示时，增加底部边距 */
.message-list.more-panel-visible {
	/* 输入框高度 + 面板高度 */
	padding-bottom: 190px;
}

.messages-wrapper {
	padding: 12px 15px;
	display: flex;
	flex-direction: column;
}

/* 底部锚点 */
.bottom-anchor {
	height: 20px;
	width: 100%;
	padding-bottom: 0;
	opacity: 0;
}

/* 消息项样式 */
.message-item {
	margin-bottom: 12px;
	animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(5px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-item:last-child {
	margin-bottom: 12px;
}

.message {
	display: flex;
	position: relative;
}

.message-mine {
	flex-direction: row-reverse;
	justify-content: flex-end;
	/* 确保靠右对齐 */
}

.message-other {
	flex-direction: row;
	width: 100%;
}

.avatar {
	width: 30px;
	height: 30px;
	border-radius: 20px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-mine .avatar {
	margin-left: 10px;
}

.message-other .avatar {
	margin-right: 10px;
}

.message-bubble {
	display: flex;
	flex-direction: column;
	width: 100%;
	/* 确保气泡容器占满宽度 */
}

/* 头像和名称容器样式 */
.sender-info {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.message-mine .sender-info {
	justify-content: flex-end;
}

.message-other .sender-info {
	justify-content: flex-start;
}

.sender-name {
	font-size: 12px;
	color: #666;
	line-height: 30px;
	vertical-align: middle;
}

.message-mine .sender-name {
	margin-right: 5px;
	margin-left: 0;
	color: #666;
}

.message-content {
	border-radius: 10px;
	padding: 8px 12px;
	position: relative;
	margin: 6px 0;
	box-sizing: border-box;
}

.content-mine {
	background-color: #1a1a1a;
	border-top-right-radius: 2px;
	width: fit-content;
	/* 让宽度自适应内容 */
	margin-left: auto;
	/* 确保靠右对齐 */
}

.content-other {
	background-color: #fff;
	border-top-left-radius: 2px;
	width: 100%;
}

.message-text {
	font-size: 14px;
	line-height: 1.4;
	word-break: break-word;
}

.content-mine .message-text {
	color: #fff;
}

.content-other .message-text {
	color: #333;
}

.message-image {
	max-width: 150px;
	max-height: 200px;
	border-radius: 5px;
}

.message-time {
	font-size: 12px;
	color: #999;
	margin-top: 2px;
}

/* 点赞和踩功能 */
.feedback-buttons {
	display: flex;
}

.feedback-button {
	display: flex;
	align-items: center;
	padding: 4px 8px;
	border-radius: 15px;
	background-color: rgba(245, 245, 245, 0.7);
}

.feedback-count {
	font-size: 12px;
	color: #3a559f;
	margin-left: 3px;
}

/* 更多选项面板 */
.more-panel {
	position: fixed;
	bottom: 60px;
	left: 0;
	width: 100%;
	display: flex;
	padding: 1px 15px 15px 15px;
	background-color: #f8f8f8;
	border-top: 1px solid #eee;
	box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
	margin-bottom: 0;
	z-index: 98;
	/* 比输入框低一级，但比消息列表高 */
	animation: panelSlideIn 0.2s ease-out;
	transform-origin: bottom;
}

@keyframes panelSlideIn {
	from {
		transform: translateY(20px);
		opacity: 0;
	}

	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 当键盘显示时，确保更多面板不可见 */
.keyboard-visible~.more-panel {
	visibility: hidden;
	opacity: 0;
	transform: translateY(20px);
	transition: all 0.2s ease-out;
	pointer-events: none;
}

/* 添加平滑过渡效果 */
.more-panel {
	transition: all 0.2s ease-out;
	opacity: 1;
	transform: translateY(0);
}

/* 消息列表过渡效果 */
.message-list {
	transition: margin-bottom 0.2s ease-out;
}

.more-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 24px;
}

.more-icon {
	width: 54px;
	height: 54px;
	background-color: #fff;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.more-text {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
}

/* 输入框区域 */
.input-container {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #fff;
	z-index: 99;
	padding-bottom: calc(env(safe-area-inset-bottom));
	height: 50px;
	line-height: 50px;
	box-sizing: content-box;
}


/* 删除不必要的键盘相关样式 */
.input-wrapper {
	display: flex;
	align-items: center;
	height: 100%;
	padding: 0px 10px;
	box-sizing: border-box;
}

.input-button {
	width: 38px;
	height: 38px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.text-input-wrapper {
	flex: 1;
	margin: 0 8px;
	height: 38px;
	background-color: #f5f5f5;
	border-radius: 6px;
	padding: 0 14px;
}

.text-input {
	width: 100%;
	height: 38px;
	font-size: 15px;
}

.send-button {
	width: 64px;
	height: 38px;
	background-color: #ddd;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.send-active {
	background-color: #1a1a1a;
}

.send-text {
	font-size: 15px;
	color: #fff;
}

/* 遮罩层 */
.panel-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 98;
	background-color: transparent;
}

/* 点踩对话框样式 */
.dislike-dialog-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	box-sizing: border-box;
}

.dislike-dialog {
	width: 85%;
	max-width: 600rpx;
	max-height: 80vh;
	/* 限制最大高度，确保在小屏幕上不会超出屏幕 */
	background-color: #fff;
	border-radius: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
	box-sizing: border-box;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.dislike-dialog-content {
	padding: 24px;
	width: 100%;
	box-sizing: border-box;
	overflow-y: auto;
	/* 允许内容滚动，以防内容过多 */
	/* 添加安全区域边距，确保在全面屏上显示良好 */
	padding-bottom: calc(24px + constant(safe-area-inset-bottom));
	padding-bottom: calc(24px + env(safe-area-inset-bottom));
	display: flex;
	flex-direction: column;
}

.dislike-dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20px;
}

.header-content {
	flex: 1;
}

.price-change {
	font-size: 14px;
	color: #000;
	margin-left: 5px;
}

.dislike-dialog-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 6px;
	display: block;
}

.dislike-dialog-subtitle {
	font-size: 13px;
	color: #999;
	display: block;
}

.close-btn {
	padding: 4px;
	margin: -4px;
}

.dislike-reason-list {
	margin-bottom: 20px;
	width: 100%;
}

.reason-tags {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -4px;
	width: 100%;
	box-sizing: border-box;
}

.reason-tag {
	background-color: #f5f5f5;
	border-radius: 30px;
	padding: 8px 16px;
	margin: 0 4px 8px;
	transition: all 0.2s ease;
	position: relative;
	overflow: hidden;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.reason-tag:active {
	opacity: 0.7;
	transform: scale(0.98);
}

.reason-tag-selected {
	background-color: #3a559f;
}

.reason-tag-text {
	font-size: 14px;
	color: #333;
	white-space: nowrap;
}

.reason-tag-selected .reason-tag-text {
	color: #fff;
}

.dislike-dialog-input {
	margin-bottom: 20px;
	width: 100%;
	position: relative;
	background-color: #f5f5f5;
	border-radius: 12px;
	overflow: hidden;
}

.dislike-textarea {
	width: 100%;
	height: auto;
	min-height: 80px;
	max-height: 120px;
	font-size: 15px;
	line-height: 1.4;
	box-sizing: border-box;
	max-width: 100%;
	overflow-y: auto;
	padding: 14px;
	background-color: transparent;
}

.textarea-counter {
	font-size: 12px;
	color: #999;
	text-align: right;
	padding: 0 14px 8px;
	margin: 0;
}

.dislike-dialog-buttons {
	display: flex;
	justify-content: flex-end;
	margin-top: 8px;
}

.dislike-btn {
	padding: 10px 24px;
	border-radius: 30px;
	margin-left: 12px;
	font-size: 15px;
	font-weight: 500;
	transition: all 0.2s ease;
}

.dislike-btn:active {
	opacity: 0.8;
	transform: scale(0.97);
}

.dislike-cancel-btn {
	background-color: #f0f0f0;
	color: #666;
}

.dislike-submit-btn {
	background-color: #1a1a1a;
	color: #fff;
}

/* 图文消息样式 */
.rich-message {
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
	width: 100%;
}

.rich-title {
	font-size: 15px;
	font-weight: 600;
	color: #333;
	margin-bottom: 5px;
}

.rich-text {
	display: block;
	font-size: 12px;
	color: #666;
	line-height: 18px;
	text-align: left;
	margin-bottom: 6px;
	padding: 5px 0;
	/* text-indent: 2em; */
}

.rich-image {
	width: 100%;
	max-height: 150px;
	margin-top: 10px;
}

.rich-link {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 12px;
	border-top: 1px solid #eee;
}

.rich-link-text {
	font-size: 13px;
	color: #3a559f;
}

/* 更新弹窗媒体查询 */
@media screen and (max-width: 375px) {
	.reason-tag {
		padding: 6px 12px;
	}

	.reason-tag-text {
		font-size: 13px;
	}

	.dislike-dialog {
		width: 90%;
		max-width: 90%;
	}

	.dislike-dialog-content {
		padding: 20px;
	}
}

/* 语音输入相关样式 */
.voice-input-button {
	flex: 1;
	height: 34px;
	border-radius: 6px;
	margin: 0 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	user-select: none;
	touch-action: none;
	border: 1px solid #f2f2f2;
	transition: all 0.2s;
}

.voice-input-button:active {
	background-color: #f0f0f0;
	transform: scale(0.98);
}

.voice-button-text {
	font-size: 14px;
	color: #555;
	font-weight: 500;
}

/* 语音消息样式 */
.voice-message {
	display: flex;
	align-items: center;
	border-radius: 18px;
	background-color: #1a1a1a;
	max-width: 120px;
	min-width: 80px;
	height: 20px;
	text-align: right;
}

.content-mine .voice-message {
	flex-direction: row-reverse;
	background-color: #1a1a1a;
}

.content-other .voice-message {
	background-color: #fff;
}

.voice-icon {
	width: 30px;
	height: 30px;
	/* margin: 0 6px; */
	/* flex-shrink: 0; */
}

.voice-duration {
	font-size: 13px;
	color: #fff;
	/* margin: 0 6px; */
}

.content-other .voice-duration {
	color: #fff;
}

.voice-playing {
	display: flex;
	align-items: center;
	margin-left: 4px;
}

.voice-animate {
	animation: voicePulse 1s infinite alternate;
}

@keyframes voicePulse {
	0% {
		opacity: 0.5;
		transform: scale(0.8);
	}

	100% {
		opacity: 1;
		transform: scale(1.1);
	}
}

/* 产品图片网格布局 */
.product-images-grid {
	overflow: hidden;
	box-sizing: border-box;
}

.product-list-view {
	display: flex;
	flex-direction: column;
}

.product-item {
	background-color: #fff;
	overflow: hidden;
	box-sizing: border-box;
	padding: 8px 0;
}

.product-card {
	/* padding: 5px 0; */
	background-color: #fff;
	position: relative;
	box-sizing: border-box;
}

.product-header {
	display: flex;
	align-items: flex-start;
	position: relative;
	box-sizing: border-box;
}

.product-info {
	flex: 1;
	margin-left: 8px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

.product-title-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.product-title {
	font-size: 14px;
	font-weight: bold;
	color: #1a1a1a;
	flex: 1;
}

.reference-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.reference-number {
	font-size: 12px;
	color: #666;
}

.expand-btn {
	display: flex;
	align-items: center;
	padding: 2px 8px;
	background-color: #f5f5f5;
	border-radius: 4px;
}

.expand-text {
	font-size: 12px;
	color: #1a1a1a;
	margin-right: 4px;
}

.price-row {
	display: flex;
	align-items: center;
	width: 100%;
}

.price-row-1 {
	margin-top: 5px;
}


.price-label {
	font-size: 14px;
	color: #1a1a1a;
}

.price-value {
	font-size: 12px;
	margin-left: 10px;
	color: #1a1a1a;
	font-weight: bold;
}

.price-up {
	color: #f44336;
}

.price-down {
	color: #4caf50;
}

.spec-tags {
	display: flex;
	align-items: center;
	background-color: #f9f9f9;
	border-radius: 8px;
	margin: 15px 0;
	justify-content: space-between;
	box-sizing: border-box;
	width: 100%;
}

.specs-left {
	display: flex;
	flex-wrap: nowrap;
	overflow-x: auto;
}

.price-info {
	display: flex;
	flex-direction: column;
	width: 100%;
}

.trend-section {
	background-color: white;
	padding: 0;
	box-shadow: none;
	width: 100%;
	box-sizing: border-box;
}

.trend-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
	flex-wrap: wrap;
	gap: 8px;
}

.trend-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	flex-shrink: 0;
}

.period-tabs {
	display: flex;
	background-color: #f5f5f5;
	border-radius: 2px;
	overflow: hidden;
	flex-shrink: 0;
	max-width: 75%;
}

.period-tab {
	padding: 4px 14px !important;
	font-size: 12px;
	color: #666;
	white-space: nowrap;
}

.period-active {
	background-color: #1a1a1a;
	color: white;
}

.trend-chart {
	width: 100%;
	border-radius: 8px;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	box-sizing: border-box;
	position: relative;
	margin: 10px 0;
	overflow: hidden;
}

.chart-image {
	width: 100%;
	height: auto;
	object-fit: contain;
}

.price-stats {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12px;
	width: 100%;
	box-sizing: border-box;
	flex-wrap: wrap;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 32%;
	box-sizing: border-box;
}

.stat-value {
	font-size: 14px;
	font-weight: bold;
	color: #333;
}

.stat-label {
	font-size: 12px;
	color: #999;
}

.price-change {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-top: 1px solid #f5f5f5;
	border-bottom: 1px solid #f5f5f5;
	margin-bottom: 12px;
	width: 100%;
	box-sizing: border-box;
}

.change-col {
	display: flex;
	flex-direction: column;
}

.change-value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.change-label {
	font-size: 12px;
	color: #666;
	margin-top: 4px;
}

.change-amount {
	font-size: 14px;
	font-weight: 500;
	text-align: right;
}

.change-percent {
	font-size: 12px;
	margin-top: 4px;
	text-align: right;
}

.negative {
	color: #4caf50;
}

.suggested-price {
	margin-top: 15px;
	width: 100%;
	box-sizing: border-box;
}

.suggest-header {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
	width: 100%;
}

.suggest-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
}

.price-range {
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 8px;
	padding: 12px;
	width: 100%;
	box-sizing: border-box;
}

.range-value {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.range-date {
	font-size: 12px;
	color: #999;
}

/* 手表详情底部弹窗 */
.specs-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
	box-sizing: border-box;
	animation: fadeIn 0.2s ease-out;
}

.specs-content {
	width: 100%;
	height: auto;
	max-height: 70vh;
	background-color: #fff;
	border-radius: 16px 16px 0 0;
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease-out;
	overflow: hidden;
	padding-bottom: env(safe-area-inset-bottom);
}

.specs-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f0f0f0;
}

.specs-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.close-specs {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.specs-list {
	flex: 1;
	height: auto;
	max-height: calc(70vh - 50px);
}

.specs-inner {
	padding: 16px 16px 0;
}

.specs-section {
	margin-bottom: 24px;
	width: 100%;
}

.section-title {
	font-size: 15px;
	font-weight: bold;
	color: #333;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid #f5f5f5;
	display: block;
	width: 100%;
}

.spec-items-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	margin-bottom: 8px;
}

.spec-item {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 12px;
	padding-bottom: 12px;
	border-bottom: 1px solid #f5f5f5;
	width: 100%;
	box-sizing: border-box;
}

.spec-label {
	font-size: 14px;
	color: #666;
	width: 35%;
	padding-right: 10px;
	line-height: 1.4;
	flex-shrink: 0;
}

.spec-value {
	font-size: 14px;
	color: #999;
	width: 65%;
	text-align: right;
	line-height: 1.4;
	word-break: break-word;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
	word-wrap: break-word;
}

/* 添加底部安全区域 */
.safe-area-bottom {
	height: 30px;
	width: 100%;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

/* 在小屏幕上调整规格项的宽度 */
@media screen and (max-width: 340px) {
	.spec-label {
		width: 35%;
	}

	.spec-value {
		width: 65%;
	}
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
	.product-thumbnail {
		width: 75px !important;
		height: 75px !important;
	}

	.product-info {
		margin-left: 8px;
		width: calc(100% - 83px);
	}

	.product-title {
		font-size: 13px;
	}

	.reference-number {
		font-size: 11px;
	}

	.expand-btn {
		padding: 1px 6px;
	}

	.expand-text {
		font-size: 11px;
	}

	.price-label {
		font-size: 11px;
	}

	.price-value {
		font-size: 13px;
	}

	.spec-tag {
		padding: 2px 8px;
		min-width: 65px;
	}

	.spec-value-text {
		font-size: 12px;
	}

	.spec-title-text {
		font-size: 10px;
	}

	.trend-title {
		font-size: 13px;
	}

	.period-tab {
		padding: 3px 6px;
		font-size: 11px;
	}

	.spec-label,
	.spec-value {
		font-size: 13px;
	}
}

/* 产品缩略图样式 */
.product-thumbnail {
	width: 80px !important;
	height: 80px !important;
	border-radius: 4px;
	object-fit: contain;
	/* background-color: #f9f9f9; */
	flex-shrink: 0;
}

/* Spec Tag Styles for Vertical Layout */
.spec-tag {
	display: flex !important;
	flex-direction: column !important;
	align-items: center !important;
	justify-content: center !important;
	background-color: transparent !important;
	border-radius: 6px !important;
	padding: 10px !important;
	width: 25% !important;
	/* 固定宽度为25%，确保显示4个 */
	min-width: unset !important;
	/* 移除最小宽度限制 */
	text-align: center !important;
	flex-shrink: 0 !important;
	box-sizing: border-box !important;
}

.spec-tag .spec-title-text {
	display: block !important;
	font-size: 11px !important;
	color: #999 !important;
	width: 100% !important;
	overflow: hidden !important;
	text-overflow: ellipsis !important;
	white-space: nowrap !important;
	text-align: center !important;
}

.spec-tag .spec-value-text {
	display: block !important;
	font-size: 13px !important;
	font-weight: bold !important;
	color: #1a1a1a !important;
	width: 100% !important;
	overflow: hidden !important;
	text-overflow: ellipsis !important;
	white-space: nowrap !important;
	text-align: center !important;
}

.specs-left {
	display: flex !important;
	flex-wrap: nowrap !important;
	flex: 1 !important;
	gap: 0 !important;
	/* 移除间隙以确保平均分布 */
	overflow-x: hidden !important;
	/* 防止横向滚动 */
	min-width: 0 !important;
	max-width: calc(100% - 30px) !important;
	justify-content: space-between !important;
	/* 平均分布空间 */
}

.spec-arrow {
	margin-right: 5px;
}

.price-check-dialog-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	/* align-items: center; */
	justify-content: center;
	overflow: hidden;
}

.price-check-dialog {
	width: 300px;
	height: 320px;
	min-height: 320px;
	background-image: url('https://www.zhida.net/app-resource/icon/price-check.png');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 140px 0 0 0;
	margin-top: 50px;
}

.dialog-title {
	font-size: 20px;
	color: #1a1a1a;
	font-weight: bold;
}

.watch-image {
	width: 120px;
	height: 80px;
}

.watch-name {
	font-size: 14px;
	color: #1a1a1a;
	font-weight: 500;
	text-align: center;
}

.watch-reference {
	font-size: 14px;
	color: #1a1a1a;
	margin-bottom: 10px;
}

.watch-price-range {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5px;
}

.watch-price-range-text {
	font-size: 20px;
	color: #1a1a1a;
	font-weight: bold;
}

.watch-price-label {
	font-size: 14px;
	color: #666;
}

.feedback-hint {
	font-size: 10px;
	color: #ff6b6b;
	text-align: center;
	padding: 10px 20px;
}

.action-buttons {
	display: flex;
	width: 100%;
	justify-content: center;
	box-sizing: border-box;
}

.action-button {
	padding: 10px 30px;
	font-size: 12px;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	color: white;
	text-align: center;
	position: relative;
	overflow: hidden;
	margin-top: 10px;
}

.action-button text {
	position: relative;
	z-index: 2;
	color: #fff;
}

.accurate {
	background-color: #c8a882;
	border-radius: 25px 0px 0px 25px;
	clip-path: polygon(0 0, 100% 0, calc(100% - 15px) 100%, 0 100%);
}

.inaccurate {
	background-color: #9d9d9d;
	border-radius: 0px 25px 25px 0px;
	clip-path: polygon(15px 0, 100% 0, 100% 100%, 0 100%);
}
</style>