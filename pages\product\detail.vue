<template>
    <view class="product-detail-container">

        <!-- 内容区域 -->
        <scroll-view class="content-scroll" scroll-y="true">
            <!-- 轮播图 -->
            <swiper class="image-swiper" :autoplay="true" :interval="3000" :duration="500">
                <swiper-item v-for="(item, index) in product.wachesNewImages" :key="index">
                    <image class="product-image" :src="item.originalUrl" mode="aspectFit"></image>
                </swiper-item>
            </swiper>

            <!-- 产品基础信息 -->
            <view class="product-base-info">
                <view class="product-header first-header">
                    <view class="product-title product-title-first">
                        <text class="product-name product-name-first">{{ (product.brand || '') + ' ' + (product.series || '') + ' ' + (product.movementType || '') +  ' ' +(product.caseDiameter || '') +  ' ' + (product.dialColor || '') +  ' ' + (product.caseMaterial || '') }}</text>
                    </view>
                    <view class="favorite-btn">
                        <uni-icons type="star-filled" v-if="product.watchesLike && product.watchesLike.length > 0" size="24" color="#d6b391" @click="toggleFavorite"></uni-icons>
                        <uni-icons type="star" v-else size="24" color="#999" @click="toggleFavorite"></uni-icons>
                    </view>
                </view>
                <view class="product-header">
                    <view class="product-title">
                        <text class="product-name product-name-bold">{{ product.referenceNumber || '' }}</text>
                        <view class="copy-btn" @click="copyReferenceNumber">
                            <image src="https://www.zhida.net/app-resource/icon/1624.png" style="width: 14px; height: 14px;" mode="aspectFit"></image>
                        </view>
                    </view>
                    <view class="favorite-btn">
                        <text class="like-count">{{ product.likeNum || 0 }}</text>
                    </view>
                </view>
                
                <view class="price-section">
                    <view class="price-column">
                        <text class="price-label">官方指导价</text>
                        <text class="official-price">¥{{ product.officePrice || '' }}</text>
                    </view>
                    
                    <view class="price-column">
                        <text class="price-label">近期成交价</text>
                        <view class="market-price-container">
                            <text class="market-price">¥{{ product.watchesQuote.usedMarketPrice || '' }}</text>
                            <view class="price-trend" v-if="product.watchesQuote.changePercent && product.watchesQuote.changePercent != '--'">

                                <image v-if="product.watchesQuote.changePercent > 0" src="https://www.zhida.net/app-resource/icon/up.png" style="width: 10px; height: 10px;"
                                    mode="aspectFit"></image>
                                <image v-if="product.watchesQuote.changePercent < 0" src="https://www.zhida.net/app-resource/icon/down.png" style="width: 10px; height: 10px;"
                                    mode="aspectFit"></image>

                                <text :class="{'trend-up': product.watchesQuote.changePercent > 0, 'trend-down': product.watchesQuote.changePercent < 0}">{{ Math.abs(product.watchesQuote.changePercent || 0) }}%</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 产品参数 -->
            <view class="watch-detail-section">
                <!-- 表盘参数标签行 -->
                <view class="spec-tags" @tap="showSpecsSheet">
                    <view class="specs-left">
                        <view class="spec-tag">
                            <text class="spec-value-text">{{ product.movement.diameter || '--' }}</text>
                            <text class="spec-title-text">机芯直径</text>
                        </view>
                        <view class="spec-tag">
                            <text class="spec-value-text">{{ product.material.caseMaterial || '--' }}</text>
                            <text class="spec-title-text">表壳材质</text>
                        </view>
                        <view class="spec-tag">
                            <text class="spec-value-text">{{ product.appearance.dialColor || '--' }}</text>
                            <text class="spec-title-text">表盘颜色</text>
                        </view>
                        <view class="spec-tag">
                            <text class="spec-value-text">{{ product.movement.movementType || '--' }}</text>
                            <text class="spec-title-text">机芯类型</text>
                        </view>
                    </view>
                    <view class="spec-arrow">
                        <uni-icons type="right" size="14" color="#999"></uni-icons>
                    </view>
                </view>

                <!-- 行情走势部分 -->
                <view class="trend-section">
                    <view class="trend-header">
                        <text class="trend-title">行情走势</text>
                        <view class="period-tabs">
                            <view class="period-tab" 
                                v-for="(period, i) in ['90天', '180天', '1 年']" 
                                :key="i"
                                :class="{ 'period-active': activePeriod === i }"
                                @tap="setPeriod(i)">
                                <text>{{ period }}</text>
                            </view>
                        </view>
                    </view>

                    <!-- 趋势图 -->
                    <view class="trend-chart">
                        <LineChart v-if="chartVisible" :chartData="chartData" />
                    </view>

                    <!-- 价格统计 -->
                    <view class="price-stats">
                        <view class="stat-item">
                            <text class="stat-value">¥{{ product.watchesQuote.maxUsedPrice || 0 }}</text>
                            <text class="stat-label">最高行情</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">¥{{ product.watchesQuote.latestUsedPrice || 0 }}</text>
                            <text class="stat-label">当前行情</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">¥{{ product.watchesQuote.minUsedPrice || 0 }}</text>
                            <text class="stat-label">最低行情</text>
                        </view>
                    </view>

                    <!-- 价格变化 -->
                    <view class="price-stats">
                        <view class="stat-item">
                            <text class="stat-value">¥{{ product.watchesQuote.originUsedPrice || 0 }}</text>
                            <text class="stat-label">初始行情</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">-¥{{ product.watchesQuote.riseAmount || 0 }}</text>
                            <text class="stat-label">涨跌金额</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">{{ product.watchesQuote.risePercent || 0 }}%</text>
                            <text class="stat-label">涨跌幅</text>
                        </view>
                    </view>

                    <!-- 建议回收价 -->
                    <view class="suggested-price">
                        <view class="suggest-header">
                            <text class="suggest-title">值达建议回收价</text>
                            <uni-icons type="info" size="18" color="#999"></uni-icons>
                        </view>
                        <view class="price-range">
                            <text class="range-value">¥{{ product.watchesQuote.maxUsedPrice || 0 }} ~ ¥{{ product.watchesQuote.minUsedPrice || 0 }}</text>
                            <text class="range-date">更新时间 {{ product.watchesQuote.createTime || '--' }}</text>
                        </view>
                    </view>
                </view>
            </view>

        </scroll-view>

        <!-- 底部操作栏 -->
        <!-- <view class="footer-action-bar">
            <view class="action-btn collect-btn" @click="toggleFavorite">
                <uni-icons :type="product.watchesLike.isLike ? 'heart-filled' : 'heart'" size="22" :color="product.watchesLike.isLike ? '#d6b391' : '#666'"></uni-icons>
                <text>收藏</text>
            </view>
            <button class="buy-now-btn">行情走势</button>
        </view> -->
        
        <!-- 手表详情底部弹窗 -->
        <view class="specs-popup" v-if="showSpecsPopup" @tap.stop="closeSpecsPopup">
            <view class="specs-content" @tap.stop>
                <view class="specs-header">
                    <text class="specs-title">腕表参数</text>
                    <view class="close-specs" @tap="closeSpecsPopup">
                        <uni-icons type="close" size="22" color="#666"></uni-icons>
                    </view>
                </view>

                <scroll-view class="specs-list" scroll-y="true" show-scrollbar="false">
                    <view class="specs-inner">
                        <view class="specs-section">
                            <text class="section-title">基本参数</text>
                            <view class="spec-items-container">
                                <view class="spec-item" v-for="(item, index) in basicSpecs" :key="index">
                                    <text class="spec-label">{{ item.label }}</text>
                                    <text class="spec-value">{{ item.value }}</text>
                                </view>
                            </view>
                        </view>

                        <view class="specs-section">
                            <text class="section-title">机芯参数</text>

                            <view class="spec-items-container">
                                <view class="spec-item" v-for="(item, index) in watchSpecs" :key="index">
                                    <text class="spec-label">{{ item.label }}</text>
                                    <text class="spec-value">{{ item.value }}</text>
                                </view>
                            </view>
                        </view>

                        <view class="specs-section">
                            <text class="section-title">手表材质</text>

                            <view class="spec-items-container">
                                <view class="spec-item" v-for="(item, index) in watchMaterial" :key="index">
                                    <text class="spec-label">{{ item.label }}</text>
                                    <text class="spec-value">{{ item.value }}</text>
                                </view>
                            </view>
                        </view>

                        <view class="specs-section">
                            <text class="section-title">手表外观</text>

                            <view class="spec-items-container">
                                <view class="spec-item" v-for="(item, index) in watchAppearance" :key="index">
                                    <text class="spec-label">{{ item.label }}</text>
                                    <text class="spec-value">{{ item.value }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 底部安全区域 -->
                        <view class="safe-area-bottom"></view>
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
import { getProductDetail, toggleFavoriteWatch } from '@/utils/api.js';
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';
import LineChart from '@/components/LineChart.vue';

export default {
    components: {
        uniIcons,
        LineChart
    },
    data() {
        return {
            id: null,
            product: {
				movement: {},
				material: {},
				appearance: {},
				watchesQuote: {}
            },
            basicSpecs: [],
            watchSpecs: [],
            watchMaterial: [],
            watchAppearance: [],

            stayTime: 0, // 页面停留时间（秒）
            activePeriod: 0, // 当前选中的时间周期，默认是90天
            currentPeriod: 0, // 当前选择的周期
            chartVisible: true, // 图表是否可见
			chartData: {
				xData: [], // x轴数据
				yData: [] // y轴数据
			},
            showSpecsPopup: false, // 是否显示参数详情弹窗
        }
    },
    onLoad(options) {
        if (options.id) {
            this.id = options.id;
            this.fetchProductDetail();
        }
    },
    onShow() {
        this.realTimeStayTime();
    },
    methods: {

        // 实时统计停留秒数
        async realTimeStayTime() {
            const app = getApp();
            app.globalData.productDetailStayTime = 0;
            this.stayTimeInterval = setInterval(() => {
                app.globalData.productDetailStayTime = app.globalData.productDetailStayTime + 1;
            }, 1000);
        },

        // 获取产品详情
        fetchProductDetail() {
            uni.showLoading({
                title: '加载中...'
            });
            
            getProductDetail(this.id)
                .then(res => {
                    console.log(res);
                    this.product = res.data;
                    this.product.material ?? (this.product.material = {});
                    this.product.appearance ?? (this.product.appearance = {});
                    this.product.movement ?? (this.product.movement = {});
                    this.product.watchesQuote ?? (this.product.watchesQuote = {});
                    // this.product.watchesLike ?? (this.product.watchesLike = {isLike: 0});

                    if(!this.product.wachesNewImages || this.product.wachesNewImages.length == 0)
                    {
                        if(this.product.imageMain) {
                            this.product.wachesNewImages = [{originalUrl: this.product.imageMain}];
                        }
                    }

                    this.basicSpecs = [
                        { label: '手表品牌', value: this.product.brand || '-' },
                        { label: '手表编号', value: this.product.referenceNumber || '-' },
                        { label: '手表系列', value: this.product.series || '-' },
                        { label: '适用性别', value: this.product.gender || '-' }
                    ];

                    this.watchSpecs = [
                        { label: '机芯类型', value: this.product.movement?.movementType || '-' },
                        { label: '机芯直径', value: this.product.movement?.diameter || '-' },
                        { label: '厚度', value: this.product.movement?.thickness || '-' },
                        { label: '摆轮类型', value: this.product.movement?.balanceWheel || '-' },
                        { label: '振频', value: this.product.movement?.frequency || '-' },
                        { label: '游丝类型', value: this.product.movement?.hairspring || '-' },
                        { label: '避震', value: this.product.movement?.shockAbsorption || '-' },
                        { label: '宝石数', value: this.product.movement?.jewels || '-' },
                        { label: '零件数', value: this.product.movement?.components || '-' },
                        { label: '动力储备', value: this.product.movement?.powerReserve || '-' },
                        { label: '技术认证', value: this.product.movement?.certification || '-' }
                    ];

                    this.watchMaterial = [
                        { label: '表壳材质', value: this.product.material?.caseMaterial || '-' },
                        { label: '表盘材质', value: this.product.material?.dialMaterial || '-' },
                        { label: '表圈材质', value: this.product.material?.bezelMaterial || '-' },
                        { label: '表镜材质', value: this.product.material?.crystalMaterial || '-' },
                        { label: '表冠材质', value: this.product.material?.crownMaterial || '-' },
                        { label: '表带材质', value: this.product.material?.bandMaterial || '-' },
                        { label: '表扣材质', value: this.product.material?.claspMaterial || '-' }
                    ];

                    this.watchAppearance = [
                        { label: '手表编号', value: this.product.appearance?.referenceNumber || '-' },
                        { label: '表壳直径', value: this.product.appearance?.caseDiameter || '-' },
                        { label: '表壳厚度', value: this.product.appearance?.caseThickness || '-' },
                        { label: '表盘颜色', value: this.product.appearance?.dialColor || '-' },
                        { label: '表盘形状', value: this.product.appearance?.dialShape || '-' },
                        { label: '表带颜色', value: this.product.appearance?.bandColor || '-' },
                        { label: '表扣类型', value: this.product.appearance?.claspType || '-' },
                        { label: '表底类型', value: this.product.appearance?.caseBack || '-' },
                        { label: '手表重量', value: this.product.appearance?.weight || '-' },
                        { label: '防水性能', value: this.product.appearance?.waterResistance || '-' },
                        { label: '时标类型', value: this.product.appearance?.hourMarker || '-' },
                        { label: '表扣间距', value: this.product.appearance?.claspSpacing || '-' },
                        { label: '表耳间距', value: this.product.appearance?.lugSpacing || '-' }
                    ];

                    this.processChartData();
                })
                .catch(err => {
                    uni.showToast({
                        title: err.message || '获取产品信息失败',
                        icon: 'none'
                    });
                
                })
                .finally(() => {
                    uni.hideLoading();
                });
        },
        
        // 切换收藏状态
        toggleFavorite() {
			if (this.product.watchesLike.length > 0) {
				this.product.watchesLike = [];
				uni.showToast({
					title: '已取消收藏',
					icon: 'none'
				});
                this.product.likeNum--;
			}
            else {
				this.product.watchesLike = [1];
				uni.showToast({
					title: '已收藏',
					icon: 'none'
				});
                this.product.likeNum++;
			}

            toggleFavoriteWatch(this.id).then(res => {
                console.log(res);
            }).catch(err => {
                uni.showToast({
                    title: err.message || '收藏失败',
                })
            })
        },
        
        // 复制型号编号
        copyReferenceNumber() {
            if (this.product.referenceNumber) {
                uni.setClipboardData({
                    data: this.product.referenceNumber,
                    success: () => {
                        uni.showToast({
                            title: '已复制到剪贴板',
                            icon: 'none'
                        });
                    },
                    fail: () => {
                        uni.showToast({
                            title: '复制失败',
                            icon: 'none'
                        });
                    }
                });
            }
        },
        
		// 行情走势数据处理
		processChartData() {
			if (!this.product.watchesQuoteCount || !Array.isArray(this.product.watchesQuoteCount)) {
				// 如果没有数据，清空图表
				this.chartData = {
					xData: [],
					yData: []
				};
				return;
			}

			const trendData = this.product.watchesQuoteCount;

			// 根据当前选择的周期过滤数据
			const currentPeriod = this.getCurrentPeriod();
			const filteredData = trendData.filter(item => item.period === currentPeriod);

			if (filteredData.length === 0) {
				// 如果当前周期没有数据，清空图表
				this.chartData = {
					xData: [],
					yData: []
				};
				return;
			}

			// 按月份排序（从早到晚）
			filteredData.sort((a, b) => new Date(a.month) - new Date(b.month));

			// 生成图表数据
			const xData = filteredData.map(item => {
				const date = new Date(item.month);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
			});

			const yData = filteredData.map(item => item.avgPrice || 0);

			this.chartData = {
				xData: xData,
				yData: yData
			};

			console.log(`当前周期: ${currentPeriod}, 数据点数量: ${filteredData.length}`, this.chartData);
		},

		// 获取当前选择的周期
		getCurrentPeriod() {
			// 根据当前选择的周期返回对应的period值
			switch (this.currentPeriod) {
				case 0: // 90天
					return '90d';
				case 1: // 180天
					return '180d';
				case 2: // 1年
					return '365d';
				default:
					return '90d';
			}
		},

        // 设置时间段
		setPeriod(period) {
			this.activePeriod = period;

			// 更新当前选择的周期并重新处理图表数据
			this.currentPeriod = period;
			this.processChartData();
		},
        
        // 显示参数详情弹窗
        showSpecsSheet() {
            this.showSpecsPopup = true;
        },
        
        // 关闭参数详情弹窗
        closeSpecsPopup() {
            this.showSpecsPopup = false;
        }
    }
}
</script>

<style lang="scss">
.product-detail-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #fff;
    position: relative;
}

.custom-nav {
    position: fixed;
    top: var(--status-bar-height);
    left: 0;
    right: 0;
    z-index: 100;
    
    .nav-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 12px;
        
        .back-btn, .share-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    }
}

.content-scroll {
    flex: 1;
    margin-bottom: 20px;
}

.image-swiper {
    width: 100%;
    height: 280px;
    padding: 10px 0;
    
    .product-image {
        width: 100%;
        height: 100%;
    }
}

.product-base-info {
    background-color: #fff;
    padding: 0px 20px;
    
    .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 30px;
        position: relative;
        margin-right: -20px;
        
        .product-title, .product-title-first {
            flex: 1;
            max-width: calc(100% - 50px);
            position: relative;
            overflow: hidden;
            margin-right: 10px;
            display: flex;
            align-items: center;

            .product-name, .product-name-first {
                font-size: 16px;
                font-weight: bold;
                color: #1a1a1a;
                white-space: nowrap;
                overflow-x: auto;
                overflow-y: hidden;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */
                &::-webkit-scrollbar {
                    display: none; /* Chrome, Safari, Opera */
                }
            }

            .copy-btn {
                margin-left: 8px;
                flex-shrink: 0;
            }
            
            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                width: 30px;
                background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
                pointer-events: none;
            }
        }

        .favorite-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            border-radius: 4px;
            flex-shrink: 0;

            .like-count {
                color: #999;
                font-weight: bold;
                line-height: 1;
            }
        }
    }

    .first-header {
        margin-top: 15px;
    }
}

.price-section {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    overflow: hidden;
    
    .price-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .price-label {
            font-size: 16px;
            color: #666;
        }
        
        .official-price {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .market-price-container {
            display: flex;
            align-items: center;
            
            .market-price {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin-right: 5px;
            }
            
            .price-trend {
                display: flex;
                align-items: center;
                font-size: 16px;
                margin-left: 2px;
                .trend-up {
                    color: #f44336;
                }
                
                .trend-down {
                    color: #4caf50;
                }
            }
        }
    }
}

.watch-detail-section {
    padding: 0px 20px;   
}

/* 表盘参数标签行样式 */
.spec-tags {
	display: flex;
	align-items: center;
	background-color: #f9f9f9;
	border-radius: 8px;
	justify-content: space-between;
	box-sizing: border-box;
	width: 100%;
    margin: 25px 0;
}

.specs-left {
    display: flex;
    flex-wrap: nowrap;
    flex: 1;
    gap: 0;
    overflow-x: hidden;
    min-width: 0;
    max-width: calc(100% - 30px);
    justify-content: space-between;
}

.spec-arrow {
    margin-right: 5px;
}

.spec-tag {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border-radius: 6px;
    padding: 10px;
    width: 25%;
    min-width: unset;
    text-align: center;
    flex-shrink: 0;
    box-sizing: border-box;
}

.spec-tag .spec-title-text {
    display: block;
    font-size: 11px;
    color: #999;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.spec-tag .spec-value-text {
    display: block;
    font-size: 13px;
    font-weight: bold;
    color: #1a1a1a;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

/* 行情走势部分样式 */
.trend-section {
    background-color: white;
    padding: 0;
    box-shadow: none;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 15px;
}

/* 手表详情底部弹窗 */
.specs-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    box-sizing: border-box;
    animation: fadeIn 0.2s ease-out;
}

.specs-content {
    width: 100%;
    height: auto;
    max-height: 70vh;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
    overflow: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    margin: 0 auto;
}

.specs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.specs-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.close-specs {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.specs-list {
    flex: 1;
    height: auto;
    max-height: calc(70vh - 50px);
    width: 100%;
}

.specs-inner {
    padding: 16px 16px 0;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
}

.specs-section {
    margin-bottom: 24px;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
}

.section-title {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f5f5f5;
    display: block;
    width: 100%;
}

.spec-items-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    box-sizing: border-box;
}

.spec-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f5f5f5;
    width: 100%;
    box-sizing: border-box;
}

.spec-label {
    font-size: 14px;
    color: #666;
    width: 35%;
    padding-right: 10px;
    line-height: 1.4;
    flex-shrink: 0;
    text-align: left;
}

.spec-value {
    font-size: 14px;
    color: #999;
    width: 65%;
    text-align: right;
    line-height: 1.4;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-wrap: break-word;
    padding-right: 0;
}

/* 添加底部安全区域 */
.safe-area-bottom {
    height: 30px;
    width: 100%;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.trend-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    flex-shrink: 0;
}

.period-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 2px;
    overflow: hidden;
    flex-shrink: 0;
    max-width: 75%;
}

.period-tab {
    padding: 4px 14px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.period-active {
    background-color: #1a1a1a;
    color: white;
}

.trend-chart {
	width: 100%;
	border-radius: 8px;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	box-sizing: border-box;
	position: relative;
	margin: 10px 0;
	overflow: hidden;
}

/* 价格统计样式 */
.price-stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 32%;
    box-sizing: border-box;
}

.stat-value {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 12px;
    color: #999;
}

/* 建议回收价样式 */
.suggested-price {
    margin-top: 15px;
    width: 100%;
    box-sizing: border-box;
}

.suggest-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    width: 100%;
}

.suggest-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-right: 5px;
}

.price-range {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 8px;
    padding: 12px;
    width: 100%;
    box-sizing: border-box;
    background-color: #f9f9f9;
}

.range-value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.range-date {
    font-size: 12px;
    color: #999;
}

.footer-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 64px;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.07);
    z-index: 99;
    
    .collect-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        
        text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    }
    
    .buy-now-btn {
        flex: 1;
        height: 48px;
        border-radius: 24px;
        font-size: 16px;
        background: #1a1a1a;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(58, 81, 153, 0.3);
    }
}

/* 适配 iOS 安全区域 */
@supports (bottom: constant(safe-area-inset-bottom)) {
    .footer-action-bar {
        padding-bottom: constant(safe-area-inset-bottom);
        height: calc(64px + constant(safe-area-inset-bottom));
    }
    
    .bottom-space {
        height: calc(80px + constant(safe-area-inset-bottom));
    }
}

@supports (bottom: env(safe-area-inset-bottom)) {
    .footer-action-bar {
        padding-bottom: env(safe-area-inset-bottom);
        height: calc(64px + env(safe-area-inset-bottom));
    }
    
    .bottom-space {
        height: calc(80px + env(safe-area-inset-bottom));
    }
}
</style>