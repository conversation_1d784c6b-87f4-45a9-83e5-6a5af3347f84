<template>
	<view class="password-container">
		<view class="form-container">
			<view class="form-item">
				<text class="label">当前密码</text>
				<input type="password" class="input" placeholder="请输入当前密码" v-model="formData.oldPassword" password />
			</view>

			<view class="form-item">
				<text class="label">新密码</text>
				<input type="password" class="input" placeholder="请输入新密码" v-model="formData.newPassword" password />
			</view>

			<view class="form-item">
				<text class="label">确认新密码</text>
				<input type="password" class="input" placeholder="请再次输入新密码" v-model="formData.confirmPassword"
					password />
			</view>
		</view>

		<view class="btn-container">
			<button class="submit-btn" @click="handleSubmit">确认修改</button>
		</view>
	</view>
</template>

<script>
import { updatePassword } from '@/utils/api.js';

export default {
	data() {
		return {
			formData: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
		}
	},
	methods: {
		// 验证表单
		validateForm() {
			if (!this.formData.oldPassword) {
				uni.showToast({
					title: '请输入当前密码',
					icon: 'none'
				});
				return false;
			}

			if (!this.formData.newPassword) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				});
				return false;
			}

			if (this.formData.newPassword.length < 6) {
				uni.showToast({
					title: '新密码长度不能少于6位',
					icon: 'none'
				});
				return false;
			}

			if (this.formData.newPassword !== this.formData.confirmPassword) {
				uni.showToast({
					title: '两次输入的新密码不一致',
					icon: 'none'
				});
				return false;
			}

			return true;
		},

		// 提交修改
		async handleSubmit() {
			if (!this.validateForm()) {
				return;
			}

			try {
				uni.showLoading({
					title: '修改中...'
				});

				const res = await updatePassword({
					oldPassword: this.formData.oldPassword,
					newPassword: this.formData.newPassword
				});

				uni.hideLoading();

				if (res.code === 200) {

					setTimeout(() => {
						uni.showToast({
							title: '密码修改成功',
							icon: 'success'
						});
					}, 0)

					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '密码修改失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: error.message || '网络请求失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
.password-container {
	padding: 20px;
}

.form-container {
	background-color: #fff;
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 20px;
}

.form-item {
	margin-bottom: 20px;
}

.form-item:last-child {
	margin-bottom: 0;
}

.label {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 8px;
}

.input {
	width: 100%;
	height: 40px;
	border: 1px solid #eee;
	border-radius: 6px;
	padding: 0 15px;
	font-size: 14px;
}

.btn-container {
	padding: 0 15px;
}

.submit-btn {
	width: 100%;
	height: 44px;
	background-color: #1a1a1a;
	color: #fff;
	border-radius: 22px;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>